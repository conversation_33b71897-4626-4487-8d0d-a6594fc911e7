#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证重号数修正在分析比对中的效果
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_analysis_comparison_repeat_count():
    """测试分析比对中的重号数显示"""
    print("=== 最终验证分析比对中的重号数修正 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用用户提供的例子进行测试
    target_period = '25050'
    database_range = 200
    
    print(f"测试期号: {target_period}")
    print(f"数据范围: {database_range} 期")
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("❌ 无法获取答案数据")
        return False
    
    print(f"✓ 当前数据库: {len(current_database)} 期")
    print(f"✓ 答案数据: {len(answer_data)} 期")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"✓ 最新期: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 验证这是期号25050
    if latest_period['period'] != '25050':
        print(f"❌ 期号不匹配: 期望25050, 实际{latest_period['period']}")
        return False
    
    # 验证号码是否正确
    expected_red = [9, 12, 15, 18, 22, 33]
    expected_blue = 16
    
    if (latest_period['red_balls'] != expected_red or 
        latest_period['blue_ball'] != expected_blue):
        print(f"❌ 号码不匹配")
        print(f"   期望: {expected_red} + {expected_blue}")
        print(f"   实际: {latest_period['red_balls']} + {latest_period['blue_ball']}")
        return False
    
    print(f"✓ 号码验证通过: 25050 9 12 15 18 22 33 + 16")
    
    # 手动验证上一期
    prev_period_num = 25049
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is not None:
        prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
        prev_blue_ball = int(prev_row['b'])
        prev_red_str = ' '.join(map(str, prev_red_balls))
        print(f"✓ 上一期: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
        
        # 验证上一期号码
        expected_prev_red = [3, 6, 19, 27, 29, 33]
        expected_prev_blue = 11
        
        if (prev_red_balls != expected_prev_red or 
            prev_blue_ball != expected_prev_blue):
            print(f"❌ 上一期号码不匹配")
            print(f"   期望: {expected_prev_red} + {expected_prev_blue}")
            print(f"   实际: {prev_red_balls} + {prev_blue_ball}")
            return False
        
        print(f"✓ 上一期号码验证通过: 25049 3 6 19 27 29 33 + 11")
        
        # 计算重号
        repeat_balls = [ball for ball in latest_period['red_balls'] if ball in prev_red_balls]
        manual_repeat_count = len(repeat_balls)
        
        print(f"✓ 重号球: {repeat_balls}")
        print(f"✓ 手动计算重号数: {manual_repeat_count}")
        
        if manual_repeat_count != 1 or repeat_balls != [33]:
            print(f"❌ 重号计算错误")
            return False
        
        print(f"✓ 重号计算验证通过: 1个重号球(33)")
    else:
        print(f"❌ 未找到上一期数据")
        return False
    
    # 运行分析比对（只分析1期来测试进度显示）
    print(f"\n=== 运行分析比对测试 ===")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    # 获取筛选要求
    filter_requirements = system.prediction_engine.filter_requirements
    
    # 计算重号数
    repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    
    print(f"✓ 系统计算的重号数: {repeat_count}")
    
    if repeat_count != 1:
        print(f"❌ 系统重号数计算错误: 期望1, 实际{repeat_count}")
        return False
    
    # 显示进度信息（模拟第50期）
    print(f"\n=== 模拟第50期进度显示 ===")
    system.ui.show_analysis_progress(
        50, 100,  # 假设完成50/100期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    print(f"\n✅ 分析比对中的重号数修正验证通过！")
    return True


def main():
    """主测试函数"""
    print("分析比对重号数修正最终验证")
    print("=" * 60)
    
    success = test_analysis_comparison_repeat_count()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 重号数修正最终验证通过！")
        print("分析比对模式中的重号数计算已完全修正。")
        return True
    else:
        print("⚠️ 重号数修正验证失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
