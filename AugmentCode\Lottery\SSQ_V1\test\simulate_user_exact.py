#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全模拟用户的操作
从25001期开始，数据库范围99期，运行到第50期
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def simulate_user_exact_operation():
    """完全模拟用户的操作"""
    print("=== 完全模拟用户的操作 ===")
    print("从25001期开始，数据库范围99期，运行到第50期")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    # 调用主程序的分析比对方法
    try:
        # 获取所有可分析的期号
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        if not analysis_periods:
            print("没有可分析的期号！")
            return False
        
        print(f"需要分析比对的总期数: {len(analysis_periods)}")
        
        results = []
        
        for i, period in enumerate(analysis_periods):
            # 只运行到第50期
            if i >= 50:
                break
                
            print(f"\n--- 第{i+1}期分析 (期号: {period}) ---")
            
            # 获取当前数据库（与主程序完全相同）
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                print(f"跳过期号 {period}：无数据")
                continue
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                print(f"跳过期号 {period}：无答案数据")
                continue
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            print(f"数据库最新期: {latest_period['period']}")
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'database_size': len(current_database),
                'latest_period': latest_period
            })
            
            # 第50期显示进度（与主程序完全相同的逻辑）
            if (i + 1) == 50:
                print(f"\n=== 第{i+1}期进度显示（与用户截图对应）===")
                
                # 验证期号和号码
                if latest_period['period'] == '25050':
                    red_str = ' '.join(map(str, latest_period['red_balls']))
                    print(f"✓ 期号匹配: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
                else:
                    print(f"❌ 期号不匹配: 期望25050, 实际{latest_period['period']}")
                    return False
                
                # 获取预测用的冷球信息（包含最新1期的最近5期）
                pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)

                # 获取分析用的冷球信息（最新1期之前的5期）
                analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)

                # 获取筛选要求
                filter_requirements = system.prediction_engine.filter_requirements

                # 计算最新1期的重号数（与上期比较）
                repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
                
                print(f"计算的重号数: {repeat_count}")
                
                # 详细验证重号数计算
                print(f"\n=== 详细验证重号数计算 ===")
                
                # 手动验证
                current_period_num = int(latest_period['period'])
                prev_period_num = current_period_num - 1
                
                print(f"查找上一期: {prev_period_num}")
                
                prev_row = None
                for _, row in current_database.iterrows():
                    if row['NO'] == prev_period_num:
                        prev_row = row
                        break
                
                if prev_row is not None:
                    prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
                    prev_blue_ball = int(prev_row['b'])
                    prev_red_str = ' '.join(map(str, prev_red_balls))
                    print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
                    
                    # 计算重号
                    current_red_balls = latest_period['red_balls']
                    repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
                    manual_repeat_count = len(repeat_balls)
                    
                    print(f"当前期红球: {current_red_balls}")
                    print(f"上一期红球: {prev_red_balls}")
                    print(f"重号球: {repeat_balls}")
                    print(f"手动计算重号数: {manual_repeat_count}")
                    print(f"系统计算重号数: {repeat_count}")
                    
                    if manual_repeat_count != repeat_count:
                        print("❌ 系统重号数计算错误")
                        return False
                    
                    if repeat_count != 1:
                        print(f"❌ 重号数不正确: 期望1, 实际{repeat_count}")
                        return False
                    
                    print("✅ 重号数计算验证通过")
                else:
                    print("❌ 未找到上一期数据")
                    return False

                # 显示进度（与主程序完全相同）
                print(f"\n=== 调用用户界面显示进度 ===")
                system.ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(current_database), latest_period,
                    predictions, filter_requirements,
                    (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
                    (analysis_red_cold_balls, analysis_blue_cold_balls)
                )
                
                return repeat_count == 1
        
        return False
        
    except Exception as e:
        print(f"分析比对过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("完全模拟用户操作测试")
    print("=" * 60)
    
    success = simulate_user_exact_operation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 模拟用户操作成功，重号数计算正确！")
        print("如果用户仍看到重号数为0，可能需要检查：")
        print("1. 代码版本是否最新")
        print("2. 是否有缓存问题")
        print("3. 是否有其他干扰因素")
    else:
        print("❌ 模拟用户操作失败，需要进一步调查")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
