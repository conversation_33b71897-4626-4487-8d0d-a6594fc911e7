#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试命中数计算bug
验证用户指出的命中数计算问题
"""

import sys
from modules.comparison_engine import ComparisonEngine


def test_specific_case():
    """测试用户指出的具体案例"""
    print("=== 测试用户指出的具体案例 ===")
    
    # 用户提供的数据
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '测试方法'
    }
    
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    print(f"预测号码: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"实际号码: {answer_data[0]['red_balls']} + {answer_data[0]['blue_ball']}")
    
    # 手动计算
    pred_red_set = set(prediction['red_balls'])
    answer_red_set = set(answer_data[0]['red_balls'])
    
    red_hits_manual = len(pred_red_set & answer_red_set)
    blue_hits_manual = 1 if prediction['blue_ball'] == answer_data[0]['blue_ball'] else 0
    total_hits_manual = red_hits_manual + blue_hits_manual
    
    print(f"\n手动计算:")
    print(f"  预测红球集合: {pred_red_set}")
    print(f"  实际红球集合: {answer_red_set}")
    print(f"  红球交集: {pred_red_set & answer_red_set}")
    print(f"  红球命中数: {red_hits_manual}")
    print(f"  蓝球命中数: {blue_hits_manual}")
    print(f"  总命中数: {total_hits_manual}")
    
    # 使用比对引擎计算
    config = {}  # 空配置即可
    engine = ComparisonEngine(config)
    results = engine._compare_single_prediction(prediction, answer_data)
    
    print(f"\n比对引擎计算:")
    for result in results:
        print(f"  期号: {result['period']}")
        print(f"  红球命中数: {result['red_hits']}")
        print(f"  蓝球命中数: {result['blue_hits']}")
        print(f"  总命中数: {result['total_hits']}")
        print(f"  蓝球命中状态: {result['blue_hit_status']}")
    
    # 验证结果
    if len(results) > 0:
        result = results[0]
        if (result['red_hits'] == red_hits_manual and 
            result['blue_hits'] == blue_hits_manual and 
            result['total_hits'] == total_hits_manual):
            print(f"\n✅ 比对引擎计算正确")
            
            if total_hits_manual == 5:
                print(f"✅ 总命中数正确: {total_hits_manual}")
                return True
            else:
                print(f"❌ 总命中数错误: 期望5, 实际{total_hits_manual}")
                return False
        else:
            print(f"\n❌ 比对引擎计算错误")
            return False
    else:
        print(f"\n❌ 比对引擎没有返回结果")
        return False


def main():
    """主函数"""
    print("命中数计算bug测试")
    print("=" * 60)
    
    success = test_specific_case()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 命中数计算正确！")
        print("应该显示5球命中（红球5+蓝球0）")
    else:
        print("❌ 命中数计算有问题，需要修正")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
