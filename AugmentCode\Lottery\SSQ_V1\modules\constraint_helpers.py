# -*- coding: utf-8 -*-
"""
约束辅助函数模块 (Constraint Helpers Module)

包含复杂约束条件下的球号调整算法。
"""

from typing import List, Dict


class ConstraintHelpers:
    """约束辅助函数类"""
    
    @staticmethod
    def adjust_blue_cold_ball_with_big_constraint(blue_ball: int, target_cold_count: int, 
                                                cold_balls: List[int], target_big_count: int,
                                                method_type: str, statistical_analyzer, 
                                                markov_analyzer, bayesian_analyzer, config: Dict) -> int:
        """调整蓝球冷球数（保持大球数不变）"""
        def get_blue_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.blue_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_blue_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_blue_probability(ball)
            return 0.0
        
        def is_big_ball(ball: int) -> bool:
            return ball > config['blue_big_ball_threshold']
        
        current_big_count = 1 if is_big_ball(blue_ball) else 0
        current_cold_count = 1 if blue_ball in cold_balls else 0
        
        # 如果当前球已经满足两个条件，直接返回
        if current_big_count == target_big_count and current_cold_count == target_cold_count:
            return blue_ball
        
        # 根据约束条件筛选候选球
        candidates = []
        for ball in range(1, 17):
            ball_is_big = is_big_ball(ball)
            ball_is_cold = ball in cold_balls
            
            if (ball_is_big and target_big_count == 1) or (not ball_is_big and target_big_count == 0):
                if (ball_is_cold and target_cold_count == 1) or (not ball_is_cold and target_cold_count == 0):
                    candidates.append(ball)
        
        if not candidates:
            return blue_ball
        
        # 选择概率最高的候选球
        candidates_with_prob = [(ball, get_blue_probability(ball)) for ball in candidates]
        candidates_with_prob.sort(key=lambda x: x[1], reverse=True)
        
        return candidates_with_prob[0][0]
    
    @staticmethod
    def adjust_red_repeat_balls_with_big_constraint(red_balls: List[int], target_repeat_count: int,
                                                  target_big_count: int, latest_red_balls: List[int],
                                                  method_type: str, statistical_analyzer, 
                                                  markov_analyzer, bayesian_analyzer, config: Dict) -> List[int]:
        """调整红球重号数（保持大球数不变）"""
        def get_red_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.red_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_red_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_red_probability(ball)
            return 0.0
        
        def is_big_ball(ball: int) -> bool:
            return ball > config['red_big_ball_threshold']
        
        result_balls = red_balls.copy()
        
        # 确保大球数正确
        current_big_count = sum(1 for ball in result_balls if is_big_ball(ball))
        if current_big_count != target_big_count:
            # 调整大球数
            if current_big_count > target_big_count:
                # 减少大球
                excess = current_big_count - target_big_count
                big_balls_in_result = [ball for ball in result_balls if is_big_ball(ball)]
                big_balls_with_prob = [(ball, get_red_probability(ball)) for ball in big_balls_in_result]
                big_balls_with_prob.sort(key=lambda x: x[1])
                
                for i in range(excess):
                    result_balls.remove(big_balls_with_prob[i][0])
                
                # 补充非大球
                all_balls = list(range(1, 34))
                non_big_balls = [ball for ball in all_balls if not is_big_ball(ball) and ball not in result_balls]
                non_big_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_big_balls]
                non_big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
                
                for i in range(excess):
                    if i < len(non_big_balls_with_prob):
                        result_balls.append(non_big_balls_with_prob[i][0])
            
            elif current_big_count < target_big_count:
                # 增加大球
                needed = target_big_count - current_big_count
                all_balls = list(range(1, 34))
                big_balls_not_in_result = [ball for ball in all_balls if is_big_ball(ball) and ball not in result_balls]
                big_balls_with_prob = [(ball, get_red_probability(ball)) for ball in big_balls_not_in_result]
                big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
                
                for i in range(min(needed, len(big_balls_with_prob))):
                    result_balls.append(big_balls_with_prob[i][0])
                
                # 移除多余的非大球
                if len(result_balls) > 6:
                    non_big_balls_in_result = [ball for ball in result_balls if not is_big_ball(ball)]
                    non_big_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_big_balls_in_result]
                    non_big_balls_with_prob.sort(key=lambda x: x[1])
                    
                    excess_count = len(result_balls) - 6
                    for i in range(excess_count):
                        if i < len(non_big_balls_with_prob):
                            result_balls.remove(non_big_balls_with_prob[i][0])
        
        # 在保持大球数的前提下调整重号数
        current_repeat_count = sum(1 for ball in result_balls if ball in latest_red_balls)
        
        if current_repeat_count != target_repeat_count:
            if current_repeat_count > target_repeat_count:
                # 减少重号数
                excess = current_repeat_count - target_repeat_count
                repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red_balls]
                
                # 优先移除非大球的重号
                non_big_repeat_balls = [ball for ball in repeat_balls_in_result if not is_big_ball(ball)]
                non_big_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in non_big_repeat_balls]
                non_big_repeat_with_prob.sort(key=lambda x: x[1])
                
                removed_count = 0
                for ball, _ in non_big_repeat_with_prob:
                    if removed_count < excess:
                        result_balls.remove(ball)
                        removed_count += 1
                
                # 如果还需要移除，考虑大球重号
                if removed_count < excess:
                    big_repeat_balls = [ball for ball in repeat_balls_in_result if is_big_ball(ball)]
                    big_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in big_repeat_balls]
                    big_repeat_with_prob.sort(key=lambda x: x[1])
                    
                    for ball, _ in big_repeat_with_prob:
                        if removed_count < excess:
                            result_balls.remove(ball)
                            removed_count += 1
                
                # 补充非重号球，保持大球数
                current_big_in_result = sum(1 for ball in result_balls if is_big_ball(ball))
                needed_big = target_big_count - current_big_in_result
                needed_total = 6 - len(result_balls)
                needed_non_big = needed_total - needed_big
                
                all_balls = list(range(1, 34))
                
                # 补充大球非重号
                big_non_repeat = [ball for ball in all_balls 
                                if is_big_ball(ball) and ball not in latest_red_balls and ball not in result_balls]
                big_non_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in big_non_repeat]
                big_non_repeat_with_prob.sort(key=lambda x: x[1], reverse=True)
                
                for i in range(min(needed_big, len(big_non_repeat_with_prob))):
                    result_balls.append(big_non_repeat_with_prob[i][0])
                
                # 补充非大球非重号
                non_big_non_repeat = [ball for ball in all_balls 
                                    if not is_big_ball(ball) and ball not in latest_red_balls and ball not in result_balls]
                non_big_non_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in non_big_non_repeat]
                non_big_non_repeat_with_prob.sort(key=lambda x: x[1], reverse=True)
                
                for i in range(min(needed_non_big, len(non_big_non_repeat_with_prob))):
                    result_balls.append(non_big_non_repeat_with_prob[i][0])
            
            elif current_repeat_count < target_repeat_count:
                # 增加重号数
                needed = target_repeat_count - current_repeat_count
                repeat_balls_not_in_result = [ball for ball in latest_red_balls if ball not in result_balls]
                repeat_balls_with_prob = [(ball, get_red_probability(ball)) for ball in repeat_balls_not_in_result]
                repeat_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
                
                for i in range(min(needed, len(repeat_balls_with_prob))):
                    result_balls.append(repeat_balls_with_prob[i][0])
                
                # 移除多余的非重号球，保持大球数
                if len(result_balls) > 6:
                    current_big_in_result = sum(1 for ball in result_balls if is_big_ball(ball))
                    if current_big_in_result > target_big_count:
                        # 移除多余的大球非重号
                        big_non_repeat_in_result = [ball for ball in result_balls 
                                                  if is_big_ball(ball) and ball not in latest_red_balls]
                        big_non_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in big_non_repeat_in_result]
                        big_non_repeat_with_prob.sort(key=lambda x: x[1])
                        
                        excess_big = current_big_in_result - target_big_count
                        for i in range(min(excess_big, len(big_non_repeat_with_prob))):
                            result_balls.remove(big_non_repeat_with_prob[i][0])
                    
                    # 移除多余的非大球非重号
                    if len(result_balls) > 6:
                        non_big_non_repeat_in_result = [ball for ball in result_balls 
                                                      if not is_big_ball(ball) and ball not in latest_red_balls]
                        non_big_non_repeat_with_prob = [(ball, get_red_probability(ball)) for ball in non_big_non_repeat_in_result]
                        non_big_non_repeat_with_prob.sort(key=lambda x: x[1])
                        
                        excess_count = len(result_balls) - 6
                        for i in range(min(excess_count, len(non_big_non_repeat_with_prob))):
                            result_balls.remove(non_big_non_repeat_with_prob[i][0])
        
        return result_balls[:6]
