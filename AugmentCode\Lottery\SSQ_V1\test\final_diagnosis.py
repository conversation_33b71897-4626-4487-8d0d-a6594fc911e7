#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终诊断脚本
检查用户可能遇到的所有问题
"""

import sys
import os
import inspect
from ssq_lottery_system import SSQLotterySystem


def diagnose_repeat_count_issue():
    """诊断重号数问题"""
    print("=== 最终诊断：重号数计算问题 ===")
    
    # 1. 检查文件版本
    print("1. 检查文件版本")
    try:
        with open('ssq_lottery_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if '版本: 1.1 (重号数修正版)' in content:
                print("✓ 主程序文件版本正确")
            else:
                print("❌ 主程序文件版本不正确")
                return False
    except Exception as e:
        print(f"❌ 无法读取主程序文件: {e}")
        return False
    
    # 2. 检查方法是否存在
    print("\n2. 检查重号数计算方法")
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    if hasattr(system, '_calculate_repeat_count_for_analysis'):
        print("✓ _calculate_repeat_count_for_analysis 方法存在")
        
        # 检查方法源代码
        source = inspect.getsource(system._calculate_repeat_count_for_analysis)
        if 'prev_period_num = current_period_num - 1' in source:
            print("✓ 方法逻辑正确")
        else:
            print("❌ 方法逻辑不正确")
            return False
    else:
        print("❌ _calculate_repeat_count_for_analysis 方法不存在")
        return False
    
    # 3. 检查主程序是否调用了正确的方法
    print("\n3. 检查主程序调用")
    main_source = inspect.getsource(system._run_comparison_analysis)
    if '_calculate_repeat_count_for_analysis' in main_source:
        print("✓ 主程序调用了正确的方法")
    else:
        print("❌ 主程序没有调用正确的方法")
        return False
    
    # 4. 测试具体的重号数计算
    print("\n4. 测试具体的重号数计算")
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    analysis_periods = system.data_loader.get_analysis_periods(start_period)
    period_50 = analysis_periods[49]  # 第50期
    
    current_database = system.data_loader.get_database_for_period(period_50, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"期号: {latest_period['period']}")
    print(f"数据库大小: {len(current_database)} 期")
    
    # 检查数据库期号范围
    min_period = current_database['NO'].min()
    max_period = current_database['NO'].max()
    print(f"数据库期号范围: {min_period} - {max_period}")
    
    # 检查是否包含上一期
    prev_period_num = 25049
    if prev_period_num >= min_period:
        print(f"✓ 数据库包含上一期 {prev_period_num}")
    else:
        print(f"❌ 数据库不包含上一期 {prev_period_num}")
        return False
    
    # 测试重号数计算
    repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    print(f"系统计算重号数: {repeat_count}")
    
    # 手动验证
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is not None:
        prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
        current_red_balls = latest_period['red_balls']
        repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
        manual_repeat_count = len(repeat_balls)
        
        print(f"手动计算重号数: {manual_repeat_count}")
        print(f"重号球: {repeat_balls}")
        
        if repeat_count == manual_repeat_count == 1:
            print("✅ 重号数计算完全正确")
        else:
            print(f"❌ 重号数计算错误: 系统{repeat_count}, 手动{manual_repeat_count}")
            return False
    else:
        print("❌ 未找到上一期数据")
        return False
    
    # 5. 测试完整的进度显示
    print("\n5. 测试完整的进度显示")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    # 获取筛选要求
    filter_requirements = system.prediction_engine.filter_requirements
    
    # 显示进度
    print("\n=== 完整进度显示 ===")
    system.ui.show_analysis_progress(
        50, 75,
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    return True


def provide_solution():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("🔧 解决方案")
    print("=" * 60)
    
    print("如果用户仍然看到重号数为0，可能的原因和解决方案：")
    print()
    print("1. **Python缓存问题**")
    print("   - 删除所有__pycache__目录")
    print("   - 重新启动Python解释器")
    print("   - 重新运行程序")
    print()
    print("2. **代码版本问题**")
    print("   - 确保使用最新的修正版本")
    print("   - 检查ssq_lottery_system.py文件是否包含修正")
    print()
    print("3. **IDE缓存问题**")
    print("   - 如果使用IDE，重启IDE")
    print("   - 清除IDE的Python解释器缓存")
    print()
    print("4. **环境问题**")
    print("   - 确保在正确的目录下运行")
    print("   - 确保没有多个版本的文件")
    print()
    print("5. **强制刷新方法**")
    print("   - 在命令行中运行：python -B ssq_lottery_system.py")
    print("   - -B参数禁用.pyc文件生成")


def main():
    """主函数"""
    print("重号数问题最终诊断")
    print("=" * 60)
    
    success = diagnose_repeat_count_issue()
    
    if success:
        print("\n✅ 所有诊断测试通过！")
        print("重号数计算逻辑完全正确")
        print("系统应该显示：最新1期号码的红球重号数：1")
    else:
        print("\n❌ 发现问题！")
    
    provide_solution()
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
