#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测系统测试脚本
用于验证系统各个模块的功能
"""

import sys
import traceback
from ssq_lottery_system import SSQLotterySystem


def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载功能 ===")
    try:
        from modules.data_loader import DataLoader
        
        loader = DataLoader('lottery_data_all.xlsx')
        print(f"✓ 成功加载 {len(loader.original_database)} 期历史数据")
        
        # 测试获取最新期
        latest = loader.get_latest_period(loader.original_database)
        print(f"✓ 最新期号: {latest['period']}")
        
        # 测试获取数据库范围
        db_100 = loader.get_current_database(100)
        print(f"✓ 获取最近100期数据: {len(db_100)} 期")
        
        # 测试获取答案数据
        answer_data = loader.get_answer_data('25070')
        if answer_data:
            print(f"✓ 获取答案数据: {len(answer_data)} 期")
        else:
            print("! 无法获取答案数据（可能是最新期号）")
        
        return True
    except Exception as e:
        print(f"✗ 数据加载测试失败: {e}")
        return False


def test_statistical_analysis():
    """测试统计分析功能"""
    print("\n=== 测试统计分析功能 ===")
    try:
        from modules.data_loader import DataLoader
        from modules.statistical_analyzer import StatisticalAnalyzer
        
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_big_ball_threshold': 16,
            'blue_big_ball_threshold': 8,
            'cold_ball_periods': 5
        }
        
        loader = DataLoader('lottery_data_all.xlsx')
        analyzer = StatisticalAnalyzer(config)
        database = loader.get_current_database(100)
        
        analyzer.analyze(database)
        print(f"✓ 统计分析完成")
        
        # 测试概率计算
        red_probs = analyzer.red_ball_probabilities
        blue_probs = analyzer.blue_ball_probabilities
        print(f"✓ 红球概率计算: {len(red_probs)} 个号码")
        print(f"✓ 蓝球概率计算: {len(blue_probs)} 个号码")
        
        # 测试最大概率值
        most_probable = analyzer.get_most_probable_values()
        print(f"✓ 概率最大值计算: {most_probable}")
        
        # 测试冷球计算
        latest_period = loader.get_latest_period(database)
        red_cold, blue_cold = analyzer.get_cold_balls(latest_period)
        print(f"✓ 冷球计算: 红球{len(red_cold)}个, 蓝球{len(blue_cold)}个")
        
        return True
    except Exception as e:
        print(f"✗ 统计分析测试失败: {e}")
        traceback.print_exc()
        return False


def test_prediction_algorithms():
    """测试预测算法功能"""
    print("\n=== 测试预测算法功能 ===")
    try:
        from modules.data_loader import DataLoader
        from modules.statistical_analyzer import StatisticalAnalyzer
        from modules.markov_chain import MarkovChainAnalyzer
        from modules.bayesian_analyzer import BayesianAnalyzer
        from modules.prediction_engine import PredictionEngine
        
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1,
            'red_big_ball_threshold': 16,
            'blue_big_ball_threshold': 8,
            'cold_ball_periods': 5,
            'prediction_groups': 24,
            'answer_periods': 6
        }
        
        loader = DataLoader('lottery_data_all.xlsx')
        database = loader.get_current_database(100)
        latest_period = loader.get_latest_period(database)
        
        # 初始化分析器
        stat_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        bayesian_analyzer = BayesianAnalyzer(config)
        
        # 执行分析
        stat_analyzer.analyze(database)
        markov_analyzer.analyze(database, latest_period)
        bayesian_analyzer.analyze(database, latest_period)
        print("✓ 三种分析算法执行完成")
        
        # 生成预测
        prediction_engine = PredictionEngine(config, stat_analyzer, markov_analyzer, bayesian_analyzer)
        predictions = prediction_engine.generate_all_predictions(database, latest_period)
        print(f"✓ 生成 {len(predictions)} 组预测号码")
        
        # 验证预测结果
        for group_id, prediction in predictions.items():
            red_balls = prediction['red_balls']
            blue_ball = prediction['blue_ball']
            
            # 验证红球数量和范围
            if len(red_balls) != 6:
                raise ValueError(f"第{group_id}组红球数量错误: {len(red_balls)}")
            if not all(1 <= ball <= 33 for ball in red_balls):
                raise ValueError(f"第{group_id}组红球范围错误: {red_balls}")
            if len(set(red_balls)) != 6:
                raise ValueError(f"第{group_id}组红球有重复: {red_balls}")
            
            # 验证蓝球范围
            if not (1 <= blue_ball <= 16):
                raise ValueError(f"第{group_id}组蓝球范围错误: {blue_ball}")
        
        print("✓ 预测结果验证通过")
        
        # 显示前3组预测
        for i in range(1, 4):
            pred = predictions[i]
            red_str = ' '.join(map(str, pred['red_balls']))
            print(f"  第{i}组: {pred['method']} - {red_str} + {pred['blue_ball']}")
        
        return True
    except Exception as e:
        print(f"✗ 预测算法测试失败: {e}")
        traceback.print_exc()
        return False


def test_comparison_engine():
    """测试比对引擎功能"""
    print("\n=== 测试比对引擎功能 ===")
    try:
        from modules.data_loader import DataLoader
        from modules.statistical_analyzer import StatisticalAnalyzer
        from modules.markov_chain import MarkovChainAnalyzer
        from modules.bayesian_analyzer import BayesianAnalyzer
        from modules.prediction_engine import PredictionEngine
        from modules.comparison_engine import ComparisonEngine
        
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1,
            'red_big_ball_threshold': 16,
            'blue_big_ball_threshold': 8,
            'cold_ball_periods': 5,
            'prediction_groups': 24,
            'answer_periods': 6
        }
        
        loader = DataLoader('lottery_data_all.xlsx')
        
        # 使用历史数据进行测试
        database = loader.get_database_for_period('25070', 100)
        latest_period = loader.get_latest_period(database)
        answer_data = loader.get_answer_data('25070')
        
        if not answer_data:
            print("! 跳过比对测试（无答案数据）")
            return True
        
        # 生成预测
        stat_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        bayesian_analyzer = BayesianAnalyzer(config)
        
        stat_analyzer.analyze(database)
        markov_analyzer.analyze(database, latest_period)
        bayesian_analyzer.analyze(database, latest_period)
        
        prediction_engine = PredictionEngine(config, stat_analyzer, markov_analyzer, bayesian_analyzer)
        predictions = prediction_engine.generate_all_predictions(database, latest_period)
        
        # 执行比对
        comparison_engine = ComparisonEngine(config)
        comparison_results = comparison_engine.compare_predictions(predictions, answer_data)
        print(f"✓ 比对完成: {len(comparison_results)} 组预测")
        
        # 统计结果
        statistics = comparison_engine.calculate_hit_statistics(comparison_results)
        print(f"✓ 统计分析完成: 平均命中 {statistics['average_hits']:.2f} 球")
        
        # 找出最佳结果
        best_hit = 0
        best_group = None
        for group_id, result in comparison_results.items():
            max_hits = result['max_hit']['total_hits']
            if max_hits > best_hit:
                best_hit = max_hits
                best_group = group_id
        
        if best_group:
            print(f"✓ 最佳预测: 第{best_group}组命中{best_hit}球")
        
        return True
    except Exception as e:
        print(f"✗ 比对引擎测试失败: {e}")
        traceback.print_exc()
        return False


def test_system_integration():
    """测试系统集成功能"""
    print("\n=== 测试系统集成功能 ===")
    try:
        system = SSQLotterySystem('lottery_data_all.xlsx')
        print("✓ 系统初始化成功")
        
        # 测试预测模式的核心功能（不包括用户交互）
        database_range = 100
        current_database = system.data_loader.get_current_database(database_range)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 执行分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        print(f"✓ 系统预测功能正常: 生成{len(predictions)}组预测")
        
        return True
    except Exception as e:
        print(f"✗ 系统集成测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("双色球彩票预测系统 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_data_loading,
        test_statistical_analysis,
        test_prediction_algorithms,
        test_comparison_engine,
        test_system_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
