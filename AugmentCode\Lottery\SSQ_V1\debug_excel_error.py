#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel错误
验证用户截图中显示的错误数据
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_excel_error():
    """调试Excel错误"""
    print("=== 调试Excel错误 ===")
    print("验证用户截图中显示的错误数据")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 100  # 用户使用100期
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    try:
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        # 找到25067期
        target_period = '25067'
        if target_period not in analysis_periods:
            print(f"❌ 未找到期号 {target_period}")
            return False
        
        print(f"分析期号: {target_period}")
        
        # 获取25067期的数据库和答案
        current_database = system.data_loader.get_database_for_period(target_period, database_range)
        answer_data = system.data_loader.get_answer_data(target_period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        print(f"数据库大小: {len(current_database)} 期")
        print(f"答案数据: {len(answer_data)} 期")
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        print(f"\n=== 检查用户截图中的预测组 ===")
        
        # 用户截图显示的预测号码
        target_red_balls = [2, 7, 10, 22, 27, 33]
        target_blue_ball = 14
        
        print(f"目标预测号码: {target_red_balls} + {target_blue_ball}")
        
        # 查找匹配的预测组
        matching_groups = []
        
        for group_id in range(1, 25):
            if group_id in predictions:
                prediction = predictions[group_id]
                red_balls = [int(x) for x in prediction['red_balls']]  # 转换numpy类型
                blue_ball = int(prediction['blue_ball'])
                
                if red_balls == target_red_balls and blue_ball == target_blue_ball:
                    matching_groups.append(group_id)
                    print(f"找到匹配组: 第{group_id}组 ({prediction['method']})")
        
        if not matching_groups:
            print(f"❌ 未找到匹配的预测组")
            print(f"检查实际生成的预测:")
            for group_id in range(1, 5):  # 只显示前4组
                if group_id in predictions:
                    prediction = predictions[group_id]
                    red_balls = [int(x) for x in prediction['red_balls']]
                    blue_ball = int(prediction['blue_ball'])
                    print(f"  第{group_id}组: {red_balls} + {blue_ball} ({prediction['method']})")
            return False
        
        # 检查每个匹配组的比对结果
        print(f"\n=== 检查比对结果 ===")
        
        all_correct = True
        
        for group_id in matching_groups:
            if group_id in comparison_result:
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                print(f"\n第{group_id}组比对结果:")
                print(f"  预测方法: {predictions[group_id]['method']}")
                print(f"  预测号码: {target_red_balls} + {target_blue_ball}")
                print(f"  最大命中期号: {max_hit['period']}")
                print(f"  最大命中球数: {max_hit['total_hits']}")
                print(f"  红球命中数: {max_hit['red_hits']}")
                print(f"  蓝球命中数: {max_hit['blue_hits']}")
                print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
                
                if max_hit['period'] == '25073':
                    print(f"  实际号码: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
                    
                    # 手动验证
                    pred_red_set = set(target_red_balls)
                    answer_red_set = set(max_hit['answer_red_balls'])
                    red_hits_manual = len(pred_red_set & answer_red_set)
                    blue_hits_manual = 1 if target_blue_ball == max_hit['answer_blue_ball'] else 0
                    total_hits_manual = red_hits_manual + blue_hits_manual
                    
                    print(f"\n  手动验证:")
                    print(f"    预测红球集合: {pred_red_set}")
                    print(f"    实际红球集合: {answer_red_set}")
                    print(f"    红球交集: {pred_red_set & answer_red_set}")
                    print(f"    手动计算红球命中数: {red_hits_manual}")
                    print(f"    手动计算蓝球命中数: {blue_hits_manual}")
                    print(f"    手动计算总命中数: {total_hits_manual}")
                    
                    # 检查是否正确
                    expected_red_hits = 5
                    expected_blue_hits = 0
                    expected_total_hits = 5
                    expected_blue_status = "否"
                    
                    if (max_hit['red_hits'] == expected_red_hits and
                        max_hit['blue_hits'] == expected_blue_hits and
                        max_hit['total_hits'] == expected_total_hits):
                        print(f"    ✅ 第{group_id}组计算正确")
                    else:
                        print(f"    ❌ 第{group_id}组计算错误:")
                        print(f"      期望: 红{expected_red_hits}+蓝{expected_blue_hits}={expected_total_hits}球, 蓝球状态{expected_blue_status}")
                        print(f"      实际: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球, 蓝球状态{'是' if max_hit['blue_hits'] == 1 else '否'}")
                        all_correct = False
                else:
                    print(f"  ⚠️ 最大命中期号不是25073，而是{max_hit['period']}")
        
        return all_correct
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_specific_comparison():
    """测试特定的比对案例"""
    print(f"\n=== 测试特定的比对案例 ===")
    
    from modules.comparison_engine import ComparisonEngine
    
    # 用户案例的精确数据
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '测试'
    }
    
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    print(f"预测号码: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"实际号码: {answer_data[0]['red_balls']} + {answer_data[0]['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    results = engine._compare_single_prediction(prediction, answer_data)
    
    if results:
        result = results[0]
        print(f"\n比对引擎结果:")
        print(f"  红球命中数: {result['red_hits']}")
        print(f"  蓝球命中数: {result['blue_hits']}")
        print(f"  总命中数: {result['total_hits']}")
        print(f"  蓝球命中状态: {result['blue_hit_status']}")
        
        # 验证正确性
        if (result['red_hits'] == 5 and 
            result['blue_hits'] == 0 and 
            result['total_hits'] == 5 and 
            result['blue_hit_status'] == False):
            print(f"  ✅ 比对引擎计算正确")
            return True
        else:
            print(f"  ❌ 比对引擎计算错误")
            return False
    else:
        print(f"❌ 比对引擎没有返回结果")
        return False


def main():
    """主函数"""
    print("调试Excel错误")
    print("=" * 60)
    
    success1 = debug_excel_error()
    success2 = test_specific_comparison()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 调试完成，比对逻辑正确！")
        print("如果Excel仍显示错误，可能需要重新生成Excel文件")
    else:
        print("❌ 发现比对逻辑错误，需要修正")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
