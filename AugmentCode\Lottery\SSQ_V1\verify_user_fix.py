#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证用户重号数修正
确保用户看到的结果是正确的
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def verify_user_repeat_count_fix():
    """验证用户的重号数修正"""
    print("=== 验证用户重号数修正 ===")
    print("根据用户截图，模拟完全相同的运行场景")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的实际参数
    target_period = '25001'  # 根据我们的分析得出
    database_range = 99
    
    print(f"目标期号: {target_period}")
    print(f"数据库范围: {database_range} 期")
    
    # 获取分析期号列表
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    print(f"总分析期数: {len(analysis_periods)}")
    print(f"第50期分析期号: {analysis_periods[49]}")
    
    # 验证第50期
    period_50 = analysis_periods[49]
    
    if period_50 != '25050':
        print(f"❌ 第50期期号不匹配: 期望25050, 实际{period_50}")
        return False
    
    print(f"✓ 第50期期号匹配: {period_50}")
    
    # 获取第50期数据
    current_database = system.data_loader.get_database_for_period(period_50, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    # 验证数据库信息
    if len(current_database) != 99:
        print(f"❌ 数据库期数不匹配: 期望99, 实际{len(current_database)}")
        return False
    
    print(f"✓ 数据库期数匹配: {len(current_database)}")
    
    if latest_period['period'] != '25050':
        print(f"❌ 最新期号不匹配: 期望25050, 实际{latest_period['period']}")
        return False
    
    print(f"✓ 最新期号匹配: {latest_period['period']}")
    
    # 验证号码
    expected_red = [9, 12, 15, 18, 22, 33]
    expected_blue = 16
    
    if (latest_period['red_balls'] != expected_red or 
        latest_period['blue_ball'] != expected_blue):
        print(f"❌ 号码不匹配")
        print(f"   期望: {expected_red} + {expected_blue}")
        print(f"   实际: {latest_period['red_balls']} + {latest_period['blue_ball']}")
        return False
    
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"✓ 号码匹配: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 验证重号数计算
    print(f"\n=== 验证重号数计算 ===")
    
    # 查找上一期
    prev_period_num = 25049
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is None:
        print(f"❌ 未找到上一期数据: {prev_period_num}")
        return False
    
    prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
    prev_blue_ball = int(prev_row['b'])
    prev_red_str = ' '.join(map(str, prev_red_balls))
    print(f"✓ 上一期: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
    
    # 验证上一期号码
    expected_prev_red = [3, 6, 19, 27, 29, 33]
    expected_prev_blue = 11
    
    if (prev_red_balls != expected_prev_red or 
        prev_blue_ball != expected_prev_blue):
        print(f"❌ 上一期号码不匹配")
        print(f"   期望: {expected_prev_red} + {expected_prev_blue}")
        print(f"   实际: {prev_red_balls} + {prev_blue_ball}")
        return False
    
    print(f"✓ 上一期号码匹配: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
    
    # 计算重号
    current_red_balls = latest_period['red_balls']
    repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
    manual_repeat_count = len(repeat_balls)
    
    print(f"当前期红球: {current_red_balls}")
    print(f"上一期红球: {prev_red_balls}")
    print(f"重号球: {repeat_balls}")
    print(f"手动计算重号数: {manual_repeat_count}")
    
    # 验证重号球
    if repeat_balls != [33]:
        print(f"❌ 重号球不匹配: 期望[33], 实际{repeat_balls}")
        return False
    
    if manual_repeat_count != 1:
        print(f"❌ 手动重号数不匹配: 期望1, 实际{manual_repeat_count}")
        return False
    
    print(f"✓ 重号计算正确: 1个重号球(33)")
    
    # 测试系统方法
    system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    print(f"系统计算重号数: {system_repeat_count}")
    
    if system_repeat_count != 1:
        print(f"❌ 系统重号数计算错误: 期望1, 实际{system_repeat_count}")
        return False
    
    print(f"✅ 系统重号数计算正确: {system_repeat_count}")
    
    # 运行完整的进度显示
    print(f"\n=== 运行完整的进度显示 ===")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    # 获取筛选要求
    filter_requirements = system.prediction_engine.filter_requirements
    
    # 显示进度（与用户截图完全一致）
    print(f"\n=== 第50期进度显示（与用户截图一致）===")
    system.ui.show_analysis_progress(
        50, 75,  # 50/75期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), system_repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    print(f"\n✅ 验证完成！重号数修正成功！")
    print(f"用户应该看到：最新1期号码的红球重号数：1")
    
    return True


def main():
    """主函数"""
    print("用户重号数修正验证")
    print("=" * 60)
    print("模拟用户截图中的完全相同场景")
    print("验证重号数修正是否生效")
    
    success = verify_user_repeat_count_fix()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 用户重号数修正验证通过！")
        print("如果用户仍看到重号数为0，请确保：")
        print("1. 使用最新的修正代码")
        print("2. 重新启动程序")
        print("3. 清除可能的缓存")
        return True
    else:
        print("❌ 验证失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
