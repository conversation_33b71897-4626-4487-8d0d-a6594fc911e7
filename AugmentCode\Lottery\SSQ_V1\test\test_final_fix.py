#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修正
验证用户问题是否完全解决
"""

import sys
import numpy as np
from modules.comparison_engine import ComparisonEngine


def test_user_case_final():
    """测试用户案例的最终修正"""
    print("=== 测试用户案例的最终修正 ===")
    
    # 用户案例的精确数据
    user_predictions = {
        2: {
            'red_balls': [2, 7, 10, 22, 27, 33],
            'blue_ball': 14,
            'method': '9号小冷大球红球量5加法'
        },
        6: {
            'red_balls': [np.int64(2), np.int64(7), np.int64(10), np.int64(22), np.int64(27), np.int64(33)],
            'blue_ball': np.int64(14),
            'method': '9号小冷大球红球量5加法'
        },
        9: {
            'red_balls': [2, 7, 10, 22, 27, 33],
            'blue_ball': 14,
            'method': '9号小冷大球红球量5加法'
        },
        18: {
            'red_balls': [np.int64(2), np.int64(7), np.int64(10), np.int64(22), np.int64(27), np.int64(33)],
            'blue_ball': np.int64(14),
            'method': '9号小冷大球红球量5加法'
        }
    }
    
    # 25073期的实际号码
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    print("用户案例数据:")
    print(f"  预测号码: [2, 7, 10, 22, 27, 33] + 14")
    print(f"  实际号码: [2, 7, 10, 27, 30, 33] + 11 (25073期)")
    print(f"  期望结果: 红5+蓝0=5球, 蓝球状态'否'")
    
    # 使用修正后的比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    comparison_results = engine.compare_predictions(user_predictions, answer_data)
    
    print(f"\n修正后的比对结果:")
    
    expected_results = {
        'red_hits': 5,
        'blue_hits': 0,
        'total_hits': 5,
        'blue_hit_status': False,
        'period': '25073'
    }
    
    all_correct = True
    
    for group_id in [2, 6, 9, 18]:
        if group_id in comparison_results:
            group_result = comparison_results[group_id]
            max_hit = group_result['max_hit']
            
            print(f"\n第{group_id}组:")
            print(f"  最大命中球数: {max_hit['total_hits']}")
            print(f"  最大命中期号: {max_hit['period']}")
            print(f"  红球命中数: {max_hit['red_hits']}")
            print(f"  蓝球命中数: {max_hit['blue_hits']}")
            print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
            
            # 验证结果
            if (max_hit['red_hits'] == expected_results['red_hits'] and
                max_hit['blue_hits'] == expected_results['blue_hits'] and
                max_hit['total_hits'] == expected_results['total_hits'] and
                max_hit['blue_hit_status'] == expected_results['blue_hit_status'] and
                max_hit['period'] == expected_results['period']):
                print(f"  ✅ 第{group_id}组结果正确")
            else:
                print(f"  ❌ 第{group_id}组结果错误")
                all_correct = False
        else:
            print(f"❌ 第{group_id}组没有比对结果")
            all_correct = False
    
    return all_correct


def test_excel_export_simulation():
    """模拟Excel导出测试"""
    print(f"\n=== 模拟Excel导出测试 ===")
    
    # 模拟Excel导出的数据结构
    predictions = {
        2: {'red_balls': [2, 7, 10, 22, 27, 33], 'blue_ball': 14, 'method': '9号小冷大球红球量5加法'},
        6: {'red_balls': [2, 7, 10, 22, 27, 33], 'blue_ball': 14, 'method': '9号小冷大球红球量5加法'},
        9: {'red_balls': [2, 7, 10, 22, 27, 33], 'blue_ball': 14, 'method': '9号小冷大球红球量5加法'},
        18: {'red_balls': [2, 7, 10, 22, 27, 33], 'blue_ball': 14, 'method': '9号小冷大球红球量5加法'}
    }
    
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    config = {}
    engine = ComparisonEngine(config)
    
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    print("模拟Excel导出数据:")
    print("分析期号 | 预测组号 | 预测方法 | 预测红球 | 预测蓝球 | 最大命中球数 | 最大命中期号 | 红球命中数 | 蓝球命中数 | 蓝球命中状态")
    print("-" * 120)
    
    all_correct = True
    
    for group_id in [2, 6, 9, 18]:
        if group_id in comparison_results:
            prediction = predictions[group_id]
            group_result = comparison_results[group_id]
            max_hit = group_result['max_hit']
            
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            blue_status = '是' if max_hit['blue_hits'] == 1 else '否'
            
            excel_row = f"25067 | {group_id} | {prediction['method']} | {red_balls_str} | {prediction['blue_ball']} | {max_hit['total_hits']} | {max_hit['period']} | {max_hit['red_hits']} | {max_hit['blue_hits']} | {blue_status}"
            print(excel_row)
            
            # 验证Excel数据正确性
            if (max_hit['total_hits'] == 5 and
                max_hit['red_hits'] == 5 and
                max_hit['blue_hits'] == 0 and
                blue_status == '否'):
                # 正确
                pass
            else:
                print(f"  ❌ 第{group_id}组Excel数据错误")
                all_correct = False
    
    if all_correct:
        print(f"\n✅ 所有Excel导出数据正确")
        print(f"现在所有组都应该显示：最大命中球数=5，红球命中数=5，蓝球命中数=0，蓝球命中状态=否")
    else:
        print(f"\n❌ Excel导出数据仍有错误")
    
    return all_correct


def main():
    """主函数"""
    print("最终测试修正")
    print("=" * 60)
    
    success1 = test_user_case_final()
    success2 = test_excel_export_simulation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 用户问题完全解决！")
        print("\n修正总结:")
        print("1. ✅ 修正了数据类型一致性问题")
        print("2. ✅ 确保所有预测组使用相同的答案数据")
        print("3. ✅ 所有预测组现在产生一致的正确结果")
        print("4. ✅ Excel导出将显示正确的比对数据")
        print("\n用户案例的正确结果:")
        print("- 预测: [2, 7, 10, 22, 27, 33] + 14")
        print("- 实际: [2, 7, 10, 27, 30, 33] + 11 (25073期)")
        print("- 最大命中球数: 5")
        print("- 红球命中数: 5")
        print("- 蓝球命中数: 0")
        print("- 蓝球命中状态: 否")
    else:
        print("❌ 用户问题尚未完全解决")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
