#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证比对修正
确认期号选择逻辑和数据类型一致性修正是否正确
"""

import sys
import numpy as np
from modules.comparison_engine import ComparisonEngine


def verify_period_selection_fix():
    """验证期号选择修正"""
    print("=== 验证期号选择修正 ===")
    
    # 测试案例：多个期号有相同的最大命中数
    prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试方法'
    }
    
    # 构造答案数据：期号25002、25004、25006都命中3球
    answer_data = [
        {'period': '25001', 'red_balls': [1, 2, 10, 11, 12, 13], 'blue_ball': 8},  # 2球
        {'period': '25002', 'red_balls': [1, 2, 3, 10, 11, 12], 'blue_ball': 8},  # 3球 ← 最小期号
        {'period': '25003', 'red_balls': [1, 10, 11, 12, 13, 14], 'blue_ball': 8},  # 1球
        {'period': '25004', 'red_balls': [4, 5, 6, 10, 11, 12], 'blue_ball': 8},  # 3球
        {'period': '25005', 'red_balls': [10, 11, 12, 13, 14, 15], 'blue_ball': 8},  # 0球
        {'period': '25006', 'red_balls': [1, 2, 3, 10, 11, 12], 'blue_ball': 8}   # 3球
    ]
    
    print("测试数据:")
    print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"  答案数据:")
    
    max_hits = 0
    for answer in answer_data:
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer['red_balls'])
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        max_hits = max(max_hits, total_hits)
        print(f"    期号{answer['period']}: {total_hits}球 (红{red_hits}+蓝{blue_hits})")
    
    # 找到所有最大命中的期号
    max_hit_periods = []
    for answer in answer_data:
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer['red_balls'])
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        if total_hits == max_hits:
            max_hit_periods.append(answer['period'])
    
    print(f"  最大命中数: {max_hits}球")
    print(f"  最大命中期号: {max_hit_periods}")
    print(f"  期望选择: {min(max_hit_periods)} (期号最小)")
    
    # 使用修正后的比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {1: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"\n修正后的结果:")
        print(f"  选择的期号: {max_hit['period']}")
        print(f"  命中球数: {max_hit['total_hits']}")
        
        expected_period = min(max_hit_periods)
        if max_hit['period'] == expected_period and max_hit['total_hits'] == max_hits:
            print(f"  ✅ 期号选择修正正确")
            return True
        else:
            print(f"  ❌ 期号选择修正失败")
            print(f"    期望: 期号{expected_period}, {max_hits}球")
            print(f"    实际: 期号{max_hit['period']}, {max_hit['total_hits']}球")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def verify_data_type_consistency():
    """验证数据类型一致性修正"""
    print(f"\n=== 验证数据类型一致性修正 ===")
    
    # 测试不同数据类型的组合
    test_cases = [
        {
            'name': 'Python int + numpy int64',
            'prediction': {
                'red_balls': [1, 2, 3, 4, 5, 6],  # Python int
                'blue_ball': 7,
                'method': '测试1'
            },
            'answer': {
                'period': '25001',
                'red_balls': [np.int64(1), np.int64(2), np.int64(3), np.int64(10), np.int64(11), np.int64(12)],  # numpy int64
                'blue_ball': np.int64(8)
            }
        },
        {
            'name': 'numpy int64 + Python int',
            'prediction': {
                'red_balls': [np.int64(1), np.int64(2), np.int64(3), np.int64(4), np.int64(5), np.int64(6)],  # numpy int64
                'blue_ball': np.int64(7),
                'method': '测试2'
            },
            'answer': {
                'period': '25001',
                'red_balls': [1, 2, 3, 10, 11, 12],  # Python int
                'blue_ball': 8
            }
        }
    ]
    
    config = {}
    engine = ComparisonEngine(config)
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        try:
            predictions = {1: case['prediction']}
            answer_data = [case['answer']]
            
            comparison_results = engine.compare_predictions(predictions, answer_data)
            
            if 1 in comparison_results:
                group_result = comparison_results[1]
                max_hit = group_result['max_hit']
                
                print(f"  结果: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球")
                
                # 手动验证
                pred_red_set = set([int(x) for x in case['prediction']['red_balls']])
                answer_red_set = set([int(x) for x in case['answer']['red_balls']])
                expected_red_hits = len(pred_red_set & answer_red_set)
                expected_blue_hits = 1 if int(case['prediction']['blue_ball']) == int(case['answer']['blue_ball']) else 0
                expected_total_hits = expected_red_hits + expected_blue_hits
                
                if (max_hit['red_hits'] == expected_red_hits and
                    max_hit['blue_hits'] == expected_blue_hits and
                    max_hit['total_hits'] == expected_total_hits):
                    print(f"  ✅ 数据类型一致性正确")
                else:
                    print(f"  ❌ 数据类型一致性错误")
                    print(f"    期望: 红{expected_red_hits}+蓝{expected_blue_hits}={expected_total_hits}球")
                    print(f"    实际: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球")
                    all_passed = False
            else:
                print(f"  ❌ 没有比对结果")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ 出现异常: {e}")
            all_passed = False
    
    return all_passed


def verify_user_case_logic():
    """验证用户案例的逻辑"""
    print(f"\n=== 验证用户案例的逻辑 ===")
    
    # 用户案例：预测 [2, 7, 10, 22, 27, 33] + 14
    # 实际 25073期: [2, 7, 10, 27, 30, 33] + 11
    # 应该是：红5+蓝0=5球
    
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '用户案例'
    }
    
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    print("用户案例数据:")
    print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"  实际: {answer_data[0]['red_balls']} + {answer_data[0]['blue_ball']}")
    
    # 手动计算
    pred_red_set = set(prediction['red_balls'])
    answer_red_set = set(answer_data[0]['red_balls'])
    red_intersection = pred_red_set & answer_red_set
    red_hits = len(red_intersection)
    blue_hits = 1 if prediction['blue_ball'] == answer_data[0]['blue_ball'] else 0
    total_hits = red_hits + blue_hits
    
    print(f"  红球交集: {red_intersection}")
    print(f"  手动计算: 红{red_hits}+蓝{blue_hits}={total_hits}球")
    print(f"  蓝球命中状态: {'是' if blue_hits == 1 else '否'}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {1: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"\n比对引擎结果:")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  总命中数: {max_hit['total_hits']}")
        print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
        
        if (max_hit['red_hits'] == red_hits and
            max_hit['blue_hits'] == blue_hits and
            max_hit['total_hits'] == total_hits and
            max_hit['blue_hit_status'] == (blue_hits == 1)):
            print(f"  ✅ 用户案例逻辑正确")
            return True
        else:
            print(f"  ❌ 用户案例逻辑错误")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def main():
    """主函数"""
    print("验证比对修正")
    print("=" * 60)
    
    success1 = verify_period_selection_fix()
    success2 = verify_data_type_consistency()
    success3 = verify_user_case_logic()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 比对修正验证成功！")
        print("\n修正总结:")
        print("1. ✅ 期号选择逻辑：相同命中数时选择期号最小的")
        print("2. ✅ 数据类型一致性：正确处理Python和numpy类型混合")
        print("3. ✅ 用户案例逻辑：比对计算结果正确")
        print("\n用户问题解决方案:")
        print("- 重新运行主程序生成最新的Excel文件")
        print("- 新的Excel文件将显示正确的比对结果")
        print("- 所有预测组的比对逻辑现在都是一致和正确的")
    else:
        print("❌ 比对修正验证失败")
        if not success1:
            print("  - 期号选择逻辑仍有问题")
        if not success2:
            print("  - 数据类型一致性仍有问题")
        if not success3:
            print("  - 用户案例逻辑仍有问题")
    
    return success1 and success2 and success3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
