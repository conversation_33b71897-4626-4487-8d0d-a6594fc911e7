#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修正后的冷球计算
"""

import sys
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.bayesian_analyzer import BayesianAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.user_interface import UserInterface


def test_final_cold_ball():
    """最终测试修正后的冷球计算"""
    print("=== 最终测试修正后的冷球计算 ===")
    
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'red_big_ball_threshold': 16,
        'blue_big_ball_threshold': 8,
        'cold_ball_periods': 5,
        'prediction_groups': 24,
        'answer_periods': 6
    }
    
    # 加载数据
    loader = DataLoader('lottery_data_all.xlsx')
    
    # 使用特定的历史数据进行测试
    target_period = '25070'
    database_range = 100
    
    current_database = loader.get_database_for_period(target_period, database_range)
    latest_period = loader.get_latest_period(current_database)
    
    print(f"目标期号: {target_period}")
    print(f"最新期号: {latest_period['period']}")
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 初始化分析器
    stat_analyzer = StatisticalAnalyzer(config)
    markov_analyzer = MarkovChainAnalyzer(config)
    bayesian_analyzer = BayesianAnalyzer(config)
    
    # 运行分析
    stat_analyzer.analyze(current_database)
    markov_analyzer.analyze(current_database, latest_period)
    bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    prediction_engine = PredictionEngine(config, stat_analyzer, markov_analyzer, bayesian_analyzer)
    predictions = prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取两种冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = stat_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = stat_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    print(f"\n预测用冷球（包含最新1期的最近5期）:")
    print(f"  红球冷球: {pred_red_cold_balls}")
    print(f"  蓝球冷球: {pred_blue_cold_balls}")
    
    print(f"\n分析用冷球（最新1期之前的5期）:")
    print(f"  红球冷球: {analysis_red_cold_balls}")
    print(f"  蓝球冷球: {analysis_blue_cold_balls}")
    
    # 计算最新1期的冷球数
    pred_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in pred_red_cold_balls)
    pred_blue_cold_count = 1 if latest_period['blue_ball'] in pred_blue_cold_balls else 0
    
    analysis_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in analysis_red_cold_balls)
    analysis_blue_cold_count = 1 if latest_period['blue_ball'] in analysis_blue_cold_balls else 0
    
    print(f"\n最新1期号码的冷球数:")
    print(f"  基于预测用冷球: 红{pred_red_cold_count} 蓝{pred_blue_cold_count}")
    print(f"  基于分析用冷球: 红{analysis_red_cold_count} 蓝{analysis_blue_cold_count}")
    
    # 获取筛选要求
    filter_requirements = prediction_engine.filter_requirements
    
    # 模拟重号数计算
    repeat_count = 0  # 简化处理
    
    # 显示进度信息
    print(f"\n=== 模拟分析进度显示 ===")
    ui = UserInterface()
    ui.show_analysis_progress(
        50, 100,  # 假设完成50/100期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    # 验证结果
    expected_red_cold_count = 3  # 根据手动计算的结果
    expected_blue_cold_count = 1
    
    if (analysis_red_cold_count == expected_red_cold_count and 
        analysis_blue_cold_count == expected_blue_cold_count):
        print(f"\n✅ 冷球计算修正成功！")
        print(f"   最新1期号码的红球冷球数: {analysis_red_cold_count} (期望: {expected_red_cold_count})")
        print(f"   最新1期号码的蓝球冷球数: {analysis_blue_cold_count} (期望: {expected_blue_cold_count})")
        return True
    else:
        print(f"\n❌ 冷球计算仍有问题！")
        print(f"   实际: 红{analysis_red_cold_count} 蓝{analysis_blue_cold_count}")
        print(f"   期望: 红{expected_red_cold_count} 蓝{expected_blue_cold_count}")
        return False


if __name__ == "__main__":
    success = test_final_cold_ball()
    if success:
        print("\n🎉 所有测试通过！分析比对模式的冷球计算已修正完成！")
    else:
        print("\n⚠️ 测试失败，需要进一步修正。")
    sys.exit(0 if success else 1)
