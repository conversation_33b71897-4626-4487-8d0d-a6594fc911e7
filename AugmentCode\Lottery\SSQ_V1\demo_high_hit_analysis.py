#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示高命中数分析功能
运行一个小范围的分析来展示高命中数详细信息打印功能
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def demo_high_hit_analysis():
    """演示高命中数分析功能"""
    print("=== 演示高命中数分析功能 ===")
    print("运行小范围分析来展示高命中数详细信息打印功能")
    print("分析范围: 25060-25070期 (共11期)")
    print("数据库范围: 50期")
    print()
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 设置分析参数
        start_period = '25060'
        database_range = 50
        
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        # 限制分析期号数量（演示用）
        demo_periods = analysis_periods[:11]  # 只分析前11期
        
        print(f"实际分析期号: {demo_periods}")
        print(f"分析期数: {len(demo_periods)}")
        print()
        
        results = []
        
        for i, period in enumerate(demo_periods):
            print(f"正在分析期号 {period} ({i+1}/{len(demo_periods)})...")
            
            # 获取当前数据库
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                print(f"  跳过期号 {period}：无数据库数据")
                continue
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                print(f"  跳过期号 {period}：无答案数据")
                continue
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            # 检查是否有高命中数
            max_hits_in_period = 0
            for group_id in range(1, 25):
                if group_id in comparison_result:
                    group_result = comparison_result[group_id]
                    max_hit = group_result['max_hit']
                    max_hits_in_period = max(max_hits_in_period, max_hit['total_hits'])
            
            print(f"  期号 {period} 最大命中数: {max_hits_in_period}球")
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'database_size': len(current_database),
                'latest_period': latest_period
            })
        
        print(f"\n分析完成！共分析了 {len(results)} 期")
        
        # 统计所有命中数分布
        hit_distribution = {}
        total_predictions = 0
        
        for result in results:
            comparison = result['comparison']
            for group_id in range(1, 25):
                if group_id in comparison:
                    total_predictions += 1
                    max_hit = comparison[group_id]['max_hit']
                    hits = max_hit['total_hits']
                    hit_distribution[hits] = hit_distribution.get(hits, 0) + 1
        
        print(f"\n命中数分布统计 (总预测数: {total_predictions}):")
        for hits in sorted(hit_distribution.keys()):
            count = hit_distribution[hits]
            percentage = count / total_predictions * 100
            print(f"  {hits}球命中: {count}次 ({percentage:.1f}%)")
        
        # 检查是否有6球或7球命中
        high_hits = sum(hit_distribution.get(hits, 0) for hits in [6, 7])
        if high_hits > 0:
            print(f"\n🎉 发现 {high_hits} 次高命中数记录！")
        else:
            print(f"\n📊 本次演示中没有发现6球或7球的高命中记录")
            print("这是正常的，因为高命中数比较罕见")
        
        # 调用高命中数详细信息打印功能
        system._print_high_hit_details(results)
        
        return True
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("演示高命中数分析功能")
    print("=" * 60)
    
    success = demo_high_hit_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 演示完成！")
        print("\n功能说明:")
        print("1. 在完成所有分析比对后，系统会自动打印高命中数详细信息")
        print("2. 只显示6球和7球的命中记录")
        print("3. 包含完整的预测信息和命中详情")
        print("4. 如果没有高命中记录，会显示相应提示")
        print("\n使用方法:")
        print("- 运行主程序 ssq_lottery_system.py")
        print("- 选择比对分析")
        print("- 输入起始期号和数据库范围")
        print("- 分析完成后会自动显示高命中数详细信息")
    else:
        print("❌ 演示失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
