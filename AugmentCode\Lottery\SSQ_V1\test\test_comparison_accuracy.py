#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比对引擎的准确性
验证比对结果是否与实际历史数据相符
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_specific_comparison():
    """测试特定期号的比对准确性"""
    print("=== 测试比对引擎准确性 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用截图中的期号进行测试
    target_period = '25029'  # 从截图中看到的期号
    database_range = 200     # 使用200期数据
    
    print(f"测试期号: {target_period}")
    print(f"数据范围: {database_range} 期")
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("❌ 无法获取答案数据")
        return False
    
    print(f"✓ 当前数据库: {len(current_database)} 期")
    print(f"✓ 答案数据: {len(answer_data)} 期")
    
    # 显示答案数据
    print(f"\n=== 答案数据 ===")
    for i, answer in enumerate(answer_data, 1):
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"第{i}期答案: {answer['period']} {red_str} + {answer['blue_ball']}")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    red_balls_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"\n最新期: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 显示前几组预测
    print(f"\n=== 预测结果（前6组）===")
    for group_id in range(1, 7):
        prediction = predictions[group_id]
        red_str = ' '.join(map(str, prediction['red_balls']))
        print(f"第{group_id}组: {prediction['method']} {red_str} + {prediction['blue_ball']}")
    
    # 进行比对
    comparison_results = system.comparison_engine.compare_predictions(predictions, answer_data)
    
    # 详细验证前几组的比对结果
    print(f"\n=== 详细比对验证 ===")
    
    for group_id in range(1, 7):
        if group_id not in comparison_results:
            continue
            
        group_result = comparison_results[group_id]
        prediction = group_result['prediction']
        results = group_result['results']
        max_hit = group_result['max_hit']
        
        print(f"\n第{group_id}组 ({prediction['method']}):")
        pred_red = set(prediction['red_balls'])
        pred_blue = prediction['blue_ball']
        pred_red_str = ' '.join(map(str, sorted(pred_red)))
        print(f"  预测: {pred_red_str} + {pred_blue}")
        
        # 逐期验证
        max_total_hits = 0
        max_period = ""
        max_red_hits = 0
        max_blue_hits = 0
        blue_hit_count = 0
        
        for result in results:
            answer_red = set(result['answer_red_balls'])
            answer_blue = result['answer_blue_ball']
            
            # 手动计算命中数
            manual_red_hits = len(pred_red & answer_red)
            manual_blue_hits = 1 if pred_blue == answer_blue else 0
            manual_total_hits = manual_red_hits + manual_blue_hits
            
            # 验证计算是否正确
            if (manual_red_hits != result['red_hits'] or 
                manual_blue_hits != result['blue_hits'] or 
                manual_total_hits != result['total_hits']):
                print(f"    ❌ 期号{result['period']}计算错误!")
                print(f"       预期: 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
                print(f"       实际: 红{result['red_hits']}+蓝{result['blue_hits']}={result['total_hits']}")
            
            # 更新最大命中
            if manual_total_hits > max_total_hits:
                max_total_hits = manual_total_hits
                max_period = result['period']
                max_red_hits = manual_red_hits
                max_blue_hits = manual_blue_hits
            
            # 统计蓝球命中
            if manual_blue_hits == 1:
                blue_hit_count += 1
            
            answer_red_str = ' '.join(map(str, sorted(answer_red)))
            print(f"    期号{result['period']}: {answer_red_str} + {answer_blue} -> 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
        
        print(f"  最大命中: {max_total_hits}球 (期号{max_period}, 红{max_red_hits}+蓝{max_blue_hits})")
        print(f"  蓝球命中次数: {blue_hit_count}")
        
        # 验证max_hit结果
        if (max_hit['total_hits'] != max_total_hits or 
            max_hit['period'] != max_period):
            print(f"    ❌ max_hit计算错误!")
            print(f"       预期: {max_total_hits}球 期号{max_period}")
            print(f"       实际: {max_hit['total_hits']}球 期号{max_hit['period']}")
    
    return True


def test_manual_calculation():
    """手动验证特定预测的计算"""
    print(f"\n=== 手动验证计算 ===")
    
    # 模拟一个简单的预测和答案
    prediction = {
        'red_balls': [1, 6, 14, 17, 22, 26],
        'blue_ball': 16
    }
    
    answers = [
        {'period': '25030', 'red_balls': [1, 7, 14, 22, 26, 32], 'blue_ball': 5},
        {'period': '25031', 'red_balls': [6, 17, 18, 20, 22, 26], 'blue_ball': 16},
    ]
    
    print(f"测试预测: {' '.join(map(str, prediction['red_balls']))} + {prediction['blue_ball']}")
    
    for answer in answers:
        pred_red = set(prediction['red_balls'])
        answer_red = set(answer['red_balls'])
        
        red_hits = len(pred_red & answer_red)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        
        hit_balls = sorted(list(pred_red & answer_red))
        answer_red_str = ' '.join(map(str, answer['red_balls']))
        
        print(f"  期号{answer['period']}: {answer_red_str} + {answer['blue_ball']}")
        print(f"    命中红球: {hit_balls} ({red_hits}个)")
        print(f"    命中蓝球: {'是' if blue_hits else '否'} ({blue_hits}个)")
        print(f"    总命中: {total_hits}球")
    
    return True


def main():
    """主测试函数"""
    print("比对引擎准确性测试")
    print("=" * 60)
    
    success1 = test_specific_comparison()
    success2 = test_manual_calculation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 比对引擎准确性测试完成！")
        return True
    else:
        print("⚠️ 发现比对计算问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
