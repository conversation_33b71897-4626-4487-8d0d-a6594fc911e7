#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试99期数据范围下的重号数计算
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_99_range_repeat_count():
    """调试99期数据范围下的重号数计算"""
    print("=== 调试99期数据范围下的重号数计算 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的实际参数
    start_period = '25001'
    database_range = 99  # 用户使用的是99期
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    # 获取分析期号列表
    analysis_periods = system.data_loader.get_analysis_periods(start_period)
    
    print(f"总分析期数: {len(analysis_periods)}")
    
    # 找到第50期分析
    if len(analysis_periods) >= 50:
        period_50 = analysis_periods[49]  # 第50期（索引49）
        
        print(f"第50期分析期号: {period_50}")
        
        # 获取第50期的数据（使用99期范围）
        current_database = system.data_loader.get_database_for_period(period_50, database_range)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        print(f"数据库大小: {len(current_database)} 期")
        print(f"数据库最新期: {latest_period['period']}")
        red_str = ' '.join(map(str, latest_period['red_balls']))
        print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
        
        # 验证是否是25050期
        if latest_period['period'] != '25050':
            print(f"❌ 期号不匹配: 期望25050, 实际{latest_period['period']}")
            return False
        
        # 验证号码
        expected_red = [9, 12, 15, 18, 22, 33]
        expected_blue = 16
        
        if (latest_period['red_balls'] != expected_red or 
            latest_period['blue_ball'] != expected_blue):
            print(f"❌ 号码不匹配")
            return False
        
        print(f"✓ 期号和号码验证通过")
        
        # 手动查找上一期
        current_period_num = int(latest_period['period'])
        prev_period_num = current_period_num - 1
        
        print(f"\n=== 查找上一期 {prev_period_num} ===")
        
        # 检查数据库中是否包含上一期
        prev_row = None
        for _, row in current_database.iterrows():
            if row['NO'] == prev_period_num:
                prev_row = row
                break
        
        if prev_row is not None:
            prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
            prev_blue_ball = int(prev_row['b'])
            prev_red_str = ' '.join(map(str, prev_red_balls))
            print(f"✓ 找到上一期: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
            
            # 计算重号
            current_red_balls = latest_period['red_balls']
            repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
            manual_repeat_count = len(repeat_balls)
            
            print(f"当前期红球: {current_red_balls}")
            print(f"上一期红球: {prev_red_balls}")
            print(f"重号球: {repeat_balls}")
            print(f"手动计算重号数: {manual_repeat_count}")
            
            # 测试系统方法
            system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
            print(f"系统计算重号数: {system_repeat_count}")
            
            if manual_repeat_count == 1 and system_repeat_count == 1:
                print("✅ 重号数计算正确")
                return True
            elif manual_repeat_count == 1 and system_repeat_count == 0:
                print("❌ 系统重号数计算错误")
                return False
            else:
                print(f"⚠️ 意外结果: 手动{manual_repeat_count}, 系统{system_repeat_count}")
                return False
        else:
            print(f"❌ 在99期数据库中未找到上一期 {prev_period_num}")
            
            # 显示数据库的期号范围
            min_period = current_database['NO'].min()
            max_period = current_database['NO'].max()
            print(f"数据库期号范围: {min_period} - {max_period}")
            
            # 检查数据库是否包含足够的历史数据
            if prev_period_num < min_period:
                print(f"❌ 上一期 {prev_period_num} 在数据库范围之外")
                print("这是问题的根源：99期数据库不包含上一期数据")
                return False
            
            return False
    
    return False


def test_different_database_ranges():
    """测试不同数据库范围下的重号数计算"""
    print(f"\n=== 测试不同数据库范围下的重号数计算 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    start_period = '25001'
    
    # 测试不同的数据库范围
    ranges_to_test = [99, 100, 150, 200]
    
    for db_range in ranges_to_test:
        print(f"\n--- 测试数据库范围: {db_range} 期 ---")
        
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        if len(analysis_periods) >= 50:
            period_50 = analysis_periods[49]  # 第50期
            
            # 获取数据库
            current_database = system.data_loader.get_database_for_period(period_50, db_range)
            latest_period = system.data_loader.get_latest_period(current_database)
            
            if latest_period['period'] == '25050':
                # 查找上一期
                prev_period_num = 25049
                prev_row = None
                for _, row in current_database.iterrows():
                    if row['NO'] == prev_period_num:
                        prev_row = row
                        break
                
                if prev_row is not None:
                    # 计算重号数
                    system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
                    print(f"  数据库大小: {len(current_database)} 期")
                    print(f"  包含上一期: ✓")
                    print(f"  重号数: {system_repeat_count}")
                else:
                    min_period = current_database['NO'].min()
                    max_period = current_database['NO'].max()
                    print(f"  数据库大小: {len(current_database)} 期")
                    print(f"  期号范围: {min_period} - {max_period}")
                    print(f"  包含上一期: ❌ (需要期号{prev_period_num})")
                    print(f"  重号数: 0 (无法计算)")


def main():
    """主函数"""
    print("99期数据范围重号数调试")
    print("=" * 60)
    
    success = debug_99_range_repeat_count()
    test_different_database_ranges()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 重号数计算正确")
    else:
        print("❌ 发现问题：99期数据库可能不包含上一期数据")
        print("需要修正数据库范围逻辑或重号数计算逻辑")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
