#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测系统演示脚本
展示系统的主要功能
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def demo_prediction_mode():
    """演示预测选号模式"""
    print("=" * 60)
    print("          双色球预测选号模式演示")
    print("=" * 60)
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 模拟用户选择：使用最近500期数据
    database_range = 500
    print(f"使用数据范围: 最近 {database_range} 期")
    
    # 获取当前数据库
    current_database = system.data_loader.get_current_database(database_range)
    print(f"当前数据库包含: {len(current_database)} 期数据")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    red_balls_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"最新一期的号码为: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")
    
    # 获取冷球信息
    system.statistical_analyzer.analyze(current_database)
    red_cold_balls, blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    
    if red_cold_balls:
        red_cold_str = ' '.join(map(str, red_cold_balls))
        print(f"红球冷球号码为: {red_cold_str}")
    else:
        print("红球冷球号码为: 无")
    
    if blue_cold_balls:
        blue_cold_str = ' '.join(map(str, blue_cold_balls))
        print(f"蓝球冷球号码为: {blue_cold_str}")
    else:
        print("蓝球冷球号码为: 无")
    
    # 运行分析
    print("\n正在运行统计分析...")
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 显示预测要求
    most_probable = system.statistical_analyzer.get_most_probable_values()
    print(f"\n=== 预测要求 ===")
    print(f"红球大球数要求: {most_probable.get('red_big_ball_count', 0)}")
    print(f"蓝球大球数要求: {most_probable.get('blue_big_ball_count', 0)}")
    print(f"红球冷球数要求: {most_probable.get('red_cold_ball_count', 0)}")
    print(f"蓝球冷球数要求: {most_probable.get('blue_cold_ball_count', 0)}")
    print(f"红球重号数要求: {most_probable.get('red_repeat_count', 0)}")
    
    # 生成预测
    print("\n正在生成预测号码...")
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 显示预测结果
    print(f"\n=== 预测结果 ===")
    for group_id in sorted(predictions.keys()):
        prediction = predictions[group_id]
        red_balls_str = ' '.join(map(str, prediction['red_balls']))
        print(f"第{group_id:2d}组预测的号码为: {prediction['method']:<20} {red_balls_str} + {prediction['blue_ball']}")
    
    return predictions


def demo_analysis_mode():
    """演示分析比对模式"""
    print("\n" + "=" * 60)
    print("          双色球分析比对模式演示")
    print("=" * 60)
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 模拟分析设置
    target_period = '25070'  # 使用历史期号进行演示
    database_range = 200     # 使用200期数据
    
    print(f"分析目标期号: {target_period}")
    print(f"使用数据范围: {database_range} 期")
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("无法获取答案数据，跳过分析比对演示。")
        return None
    
    print(f"当前数据库包含: {len(current_database)} 期数据")
    print(f"答案数据包含: {len(answer_data)} 期数据")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    red_balls_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"当前最新1期的号码: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")
    
    # 显示答案数据
    print(f"\n=== 答案数据 ===")
    for i, answer in enumerate(answer_data, 1):
        answer_red_str = ' '.join(map(str, answer['red_balls']))
        print(f"第{i}期答案: {answer['period']} {answer_red_str} + {answer['blue_ball']}")
    
    # 运行分析
    print(f"\n正在分析期号 {target_period}...")
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 执行比对
    comparison_results = system.comparison_engine.compare_predictions(predictions, answer_data)
    
    # 显示比对结果
    print(f"\n=== 比对结果 ===")
    
    # 统计信息
    statistics = system.comparison_engine.calculate_hit_statistics(comparison_results)
    print(f"平均命中球数: {statistics['average_hits']:.2f}")
    print(f"蓝球命中次数: {statistics['blue_hit_count']}")
    print(f"蓝球命中率: {statistics['blue_hit_rate']:.2%}")
    
    # 命中分布
    print(f"\n命中分布统计:")
    for hits, count in statistics['hit_distribution'].items():
        if count > 0:
            percentage = count / statistics['total_predictions'] * 100
            print(f"  {hits}球命中: {count}次 ({percentage:.1f}%)")
    
    # 最佳预测组
    best_hit = 0
    best_groups = []
    
    for group_id, group_result in comparison_results.items():
        hits = group_result['max_hit']['total_hits']
        if hits > best_hit:
            best_hit = hits
            best_groups = [group_id]
        elif hits == best_hit:
            best_groups.append(group_id)
    
    print(f"\n最佳预测组 (命中{best_hit}球):")
    for group_id in best_groups:
        group_result = comparison_results[group_id]
        prediction = group_result['prediction']
        max_hit = group_result['max_hit']
        
        red_balls_str = ' '.join(map(str, prediction['red_balls']))
        print(f"  第{group_id}组 ({prediction['method']}): {red_balls_str} + {prediction['blue_ball']}")
        print(f"    最大命中{best_hit}球 (期号: {max_hit['period']}, 红球{max_hit['red_hits']}+蓝球{max_hit['blue_hits']})")
    
    return comparison_results


def demo_export_functionality(predictions):
    """演示导出功能"""
    print("\n" + "=" * 60)
    print("          导出功能演示")
    print("=" * 60)
    
    try:
        # 初始化系统
        system = SSQLotterySystem('lottery_data_all.xlsx')
        
        # 获取概率表格
        current_database = system.data_loader.get_current_database(500)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        probability_tables = {
            'statistical': system.statistical_analyzer.get_probability_tables(),
            'markov': system.markov_analyzer.get_probability_tables(),
            'bayesian': system.bayesian_analyzer.get_probability_tables()
        }
        
        # 导出预测结果
        system.export_manager.export_prediction_results(predictions, probability_tables, current_database)
        print("✓ 预测结果已导出到Excel文件")
        
        return True
    except Exception as e:
        print(f"✗ 导出功能演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("双色球彩票预测与分析系统 - 功能演示")
    
    try:
        # 演示预测模式
        predictions = demo_prediction_mode()
        
        # 演示分析模式
        comparison_results = demo_analysis_mode()
        
        # 演示导出功能
        if predictions:
            demo_export_functionality(predictions)
        
        print("\n" + "=" * 60)
        print("演示完成！系统功能展示结束。")
        print("=" * 60)
        
        print("\n使用说明:")
        print("1. 运行 'python ssq_lottery_system.py' 启动完整的交互式系统")
        print("2. 运行 'python test_system.py' 执行系统功能测试")
        print("3. 查看 'output' 目录中的导出文件")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
