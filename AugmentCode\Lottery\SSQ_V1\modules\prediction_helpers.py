# -*- coding: utf-8 -*-
"""
预测辅助函数模块 (Prediction Helpers Module)

包含预测引擎使用的各种辅助函数和球号调整算法。
"""

from typing import List, Dict, Tuple


class PredictionHelpers:
    """预测辅助函数类"""
    
    @staticmethod
    def adjust_blue_big_ball(blue_ball: int, target_count: int, method_type: str, 
                           statistical_analyzer, markov_analyzer, bayesian_analyzer, config: Dict) -> int:
        """调整蓝球大球数"""
        current_big_count = 1 if blue_ball > config['blue_big_ball_threshold'] else 0
        
        if current_big_count == target_count:
            return blue_ball
        
        def get_blue_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.blue_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_blue_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_blue_probability(ball)
            return 0.0
        
        if target_count == 1:
            # 需要大球
            big_balls = [ball for ball in range(1, 17) if ball > config['blue_big_ball_threshold']]
            big_balls_with_prob = [(ball, get_blue_probability(ball)) for ball in big_balls]
            big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            return big_balls_with_prob[0][0] if big_balls_with_prob else blue_ball
        else:
            # 需要非大球
            non_big_balls = [ball for ball in range(1, 17) if ball <= config['blue_big_ball_threshold']]
            non_big_balls_with_prob = [(ball, get_blue_probability(ball)) for ball in non_big_balls]
            non_big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            return non_big_balls_with_prob[0][0] if non_big_balls_with_prob else blue_ball
    
    @staticmethod
    def adjust_red_cold_balls(red_balls: List[int], target_count: int, cold_balls: List[int], 
                            method_type: str, statistical_analyzer, markov_analyzer, 
                            bayesian_analyzer, config: Dict) -> List[int]:
        """调整红球冷球数"""
        current_cold_count = sum(1 for ball in red_balls if ball in cold_balls)
        
        if current_cold_count == target_count:
            return red_balls
        
        def get_red_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.red_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_red_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_red_probability(ball)
            return 0.0
        
        result_balls = red_balls.copy()
        
        if current_cold_count > target_count:
            # 需要减少冷球数
            excess = current_cold_count - target_count
            cold_balls_in_result = [ball for ball in result_balls if ball in cold_balls]
            
            # 按概率从小到大排序，移除概率最小的冷球
            cold_balls_with_prob = [(ball, get_red_probability(ball)) for ball in cold_balls_in_result]
            cold_balls_with_prob.sort(key=lambda x: x[1])
            
            for i in range(excess):
                ball_to_remove = cold_balls_with_prob[i][0]
                result_balls.remove(ball_to_remove)
            
            # 补充非冷球
            all_red_balls = list(range(1, 34))
            non_cold_balls = [ball for ball in all_red_balls 
                            if ball not in cold_balls and ball not in result_balls]
            
            # 按概率从大到小排序
            non_cold_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_cold_balls]
            non_cold_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            
            for i in range(excess):
                if i < len(non_cold_balls_with_prob):
                    result_balls.append(non_cold_balls_with_prob[i][0])
        
        elif current_cold_count < target_count:
            # 需要增加冷球数
            needed = target_count - current_cold_count
            cold_balls_not_in_result = [ball for ball in cold_balls if ball not in result_balls]
            
            # 按概率从大到小排序
            cold_balls_with_prob = [(ball, get_red_probability(ball)) for ball in cold_balls_not_in_result]
            cold_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            
            # 添加概率最大的冷球
            for i in range(min(needed, len(cold_balls_with_prob))):
                result_balls.append(cold_balls_with_prob[i][0])
            
            # 移除多余的非冷球
            if len(result_balls) > 6:
                non_cold_balls_in_result = [ball for ball in result_balls if ball not in cold_balls]
                non_cold_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_cold_balls_in_result]
                non_cold_balls_with_prob.sort(key=lambda x: x[1])
                
                excess_count = len(result_balls) - 6
                for i in range(excess_count):
                    if i < len(non_cold_balls_with_prob):
                        result_balls.remove(non_cold_balls_with_prob[i][0])
        
        return result_balls[:6]
    
    @staticmethod
    def adjust_blue_cold_ball(blue_ball: int, target_count: int, cold_balls: List[int], 
                            method_type: str, statistical_analyzer, markov_analyzer, 
                            bayesian_analyzer, config: Dict) -> int:
        """调整蓝球冷球数"""
        current_cold_count = 1 if blue_ball in cold_balls else 0
        
        if current_cold_count == target_count:
            return blue_ball
        
        def get_blue_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.blue_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_blue_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_blue_probability(ball)
            return 0.0
        
        if target_count == 1:
            # 需要冷球
            if cold_balls:
                cold_balls_with_prob = [(ball, get_blue_probability(ball)) for ball in cold_balls]
                cold_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
                return cold_balls_with_prob[0][0]
        else:
            # 需要非冷球
            non_cold_balls = [ball for ball in range(1, 17) if ball not in cold_balls]
            non_cold_balls_with_prob = [(ball, get_blue_probability(ball)) for ball in non_cold_balls]
            non_cold_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            return non_cold_balls_with_prob[0][0] if non_cold_balls_with_prob else blue_ball
        
        return blue_ball
    
    @staticmethod
    def adjust_red_repeat_balls(red_balls: List[int], target_count: int, latest_red_balls: List[int],
                              method_type: str, statistical_analyzer, markov_analyzer, 
                              bayesian_analyzer, config: Dict) -> List[int]:
        """调整红球重号数"""
        current_repeat_count = sum(1 for ball in red_balls if ball in latest_red_balls)
        
        if current_repeat_count == target_count:
            return red_balls
        
        def get_red_probability(ball: int) -> float:
            if method_type == 'historical':
                return statistical_analyzer.red_ball_probabilities.get(ball, 0)
            elif method_type == 'markov':
                return markov_analyzer.get_red_probability(ball)
            elif method_type == 'bayesian':
                return bayesian_analyzer.get_red_probability(ball)
            return 0.0
        
        result_balls = red_balls.copy()
        
        if current_repeat_count > target_count:
            # 需要减少重号数
            excess = current_repeat_count - target_count
            repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red_balls]
            
            # 按概率从小到大排序，移除概率最小的重号球
            repeat_balls_with_prob = [(ball, get_red_probability(ball)) for ball in repeat_balls_in_result]
            repeat_balls_with_prob.sort(key=lambda x: x[1])
            
            for i in range(excess):
                ball_to_remove = repeat_balls_with_prob[i][0]
                result_balls.remove(ball_to_remove)
            
            # 补充非重号球
            all_red_balls = list(range(1, 34))
            non_repeat_balls = [ball for ball in all_red_balls 
                              if ball not in latest_red_balls and ball not in result_balls]
            
            # 按概率从大到小排序
            non_repeat_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_repeat_balls]
            non_repeat_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            
            for i in range(excess):
                if i < len(non_repeat_balls_with_prob):
                    result_balls.append(non_repeat_balls_with_prob[i][0])
        
        elif current_repeat_count < target_count:
            # 需要增加重号数
            needed = target_count - current_repeat_count
            repeat_balls_not_in_result = [ball for ball in latest_red_balls if ball not in result_balls]
            
            # 按概率从大到小排序
            repeat_balls_with_prob = [(ball, get_red_probability(ball)) for ball in repeat_balls_not_in_result]
            repeat_balls_with_prob.sort(key=lambda x: x[1], reverse=True)
            
            # 添加概率最大的重号球
            for i in range(min(needed, len(repeat_balls_with_prob))):
                result_balls.append(repeat_balls_with_prob[i][0])
            
            # 移除多余的非重号球
            if len(result_balls) > 6:
                non_repeat_balls_in_result = [ball for ball in result_balls if ball not in latest_red_balls]
                non_repeat_balls_with_prob = [(ball, get_red_probability(ball)) for ball in non_repeat_balls_in_result]
                non_repeat_balls_with_prob.sort(key=lambda x: x[1])
                
                excess_count = len(result_balls) - 6
                for i in range(excess_count):
                    if i < len(non_repeat_balls_with_prob):
                        result_balls.remove(non_repeat_balls_with_prob[i][0])
        
        return result_balls[:6]
