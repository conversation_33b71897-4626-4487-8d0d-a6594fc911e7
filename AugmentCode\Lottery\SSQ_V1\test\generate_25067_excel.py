#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门生成包含25067期的Excel文件
验证修正后的比对逻辑
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def generate_25067_excel():
    """生成包含25067期的Excel文件"""
    print("=== 生成包含25067期的Excel文件 ===")
    print("使用修正后的比对逻辑")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    try:
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        print(f"总分析期数: {len(analysis_periods)}")
        
        # 找到25067期的索引
        target_period = '25067'
        target_index = None
        
        for i, period in enumerate(analysis_periods):
            if period == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 未找到期号 {target_period}")
            return False
        
        print(f"找到25067期，索引: {target_index}")
        
        # 运行从25001到25067期的分析
        results = []
        
        for i, period in enumerate(analysis_periods[:target_index + 1]):
            if i % 10 == 0:
                print(f"分析进度: {i+1}/{target_index + 1}")
            
            # 获取当前数据库
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                continue
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                continue
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'database_size': len(current_database),
                'latest_period': latest_period
            })
            
            # 检查25067期的结果
            if period == '25067':
                print(f"\n=== 25067期详细结果 ===")
                
                # 检查第1组预测（9号小冷大球红球量5加法）
                if 1 in comparison_result:
                    group_1_result = comparison_result[1]
                    max_hit = group_1_result['max_hit']
                    
                    print(f"第1组预测:")
                    print(f"  预测号码: {predictions[1]['red_balls']} + {predictions[1]['blue_ball']}")
                    print(f"  预测方法: {predictions[1]['method']}")
                    print(f"  最大命中: {max_hit['total_hits']}球")
                    print(f"  最大命中期号: {max_hit['period']}")
                    print(f"  红球命中数: {max_hit['red_hits']}")
                    print(f"  蓝球命中数: {max_hit['blue_hits']}")
                    
                    if max_hit['period'] == '25073':
                        print(f"  实际号码: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
                        
                        if max_hit['total_hits'] == 5:
                            print(f"  ✅ 25067期第1组结果正确: 25073期5球命中")
                        else:
                            print(f"  ❌ 25067期第1组结果错误: 显示{max_hit['total_hits']}球，应该是5球")
                            return False
                    else:
                        print(f"  ⚠️ 最大命中期号不是25073，而是{max_hit['period']}")
                
                # 显示所有组的结果摘要
                print(f"\n所有组的最大命中摘要:")
                for group_id in range(1, 25):
                    if group_id in comparison_result:
                        max_hit = comparison_result[group_id]['max_hit']
                        print(f"  第{group_id}组: {max_hit['total_hits']}球 (期号{max_hit['period']})")
        
        # 保存结果到Excel
        print(f"\n=== 保存结果到Excel ===")
        system._save_analysis_results(results)
        
        print(f"✅ 已生成包含25067期正确比对结果的Excel文件")
        print(f"请检查output目录中最新的analysis_results_*.xlsx文件")
        print(f"25067期第1组应该显示：最大命中球数=5，最大命中期号=25073")
        
        return True
        
    except Exception as e:
        print(f"生成Excel过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("生成25067期Excel文件")
    print("=" * 60)
    
    success = generate_25067_excel()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 包含25067期的Excel文件生成成功！")
        print("现在应该显示正确的命中数：5球")
        print("\n请用户：")
        print("1. 打开最新生成的Excel文件")
        print("2. 查看'详细比对结果'工作表")
        print("3. 找到分析期号=25067的行")
        print("4. 验证最大命中球数=5（不是6）")
    else:
        print("❌ Excel文件生成失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
