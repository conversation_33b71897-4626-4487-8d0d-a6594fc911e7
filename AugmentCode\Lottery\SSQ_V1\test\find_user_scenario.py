#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找用户实际运行的场景
找到第50期分析时数据库最新期是25050的情况
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def find_target_period_for_user_scenario():
    """查找用户实际的目标期号"""
    print("=== 查找用户实际的目标期号 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户截图显示：
    # - 已完成：50/75 期 (66.7%)
    # - 当前最新1期的号码：25050 9 12 15 18 22 33 + 16
    # - 当前数据库包含：99 期数据
    
    # 这意味着在第50期分析时，数据库最新期是25050
    # 我们需要找到一个目标期号，使得第50期分析的数据库最新期是25050
    
    # 尝试不同的目标期号
    for target_offset in range(0, 100):
        target_period_num = 25050 - target_offset
        target_period = str(target_period_num)
        
        try:
            analysis_periods = system.data_loader.get_analysis_periods(target_period)
            
            if len(analysis_periods) >= 75:  # 确保有75期分析
                # 检查第50期分析
                if len(analysis_periods) >= 50:
                    period_50 = analysis_periods[49]  # 第50期（索引49）
                    
                    # 获取第50期的数据库
                    current_database = system.data_loader.get_database_for_period(period_50, 99)
                    latest_period = system.data_loader.get_latest_period(current_database)
                    
                    if latest_period['period'] == '25050':
                        print(f"✓ 找到匹配的目标期号: {target_period}")
                        print(f"  第50期分析期号: {period_50}")
                        print(f"  第50期数据库最新期: {latest_period['period']}")
                        print(f"  总分析期数: {len(analysis_periods)}")
                        
                        # 验证号码是否匹配
                        expected_red = [9, 12, 15, 18, 22, 33]
                        expected_blue = 16
                        
                        if (latest_period['red_balls'] == expected_red and 
                            latest_period['blue_ball'] == expected_blue):
                            print(f"  ✓ 号码匹配: {latest_period['red_balls']} + {latest_period['blue_ball']}")
                            return target_period, period_50
                        else:
                            print(f"  ❌ 号码不匹配")
                            
        except Exception as e:
            continue
    
    print("❌ 未找到匹配的目标期号")
    return None, None


def test_user_scenario_repeat_count(target_period, period_50):
    """测试用户场景下的重号数计算"""
    print(f"\n=== 测试用户场景下的重号数计算 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    print(f"目标期号: {target_period}")
    print(f"第50期分析期号: {period_50}")
    
    # 获取第50期的数据
    current_database = system.data_loader.get_database_for_period(period_50, 99)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"第50期数据库最新期: {latest_period['period']}")
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 手动查找上一期
    current_period_num = int(latest_period['period'])
    prev_period_num = current_period_num - 1
    
    print(f"查找上一期: {prev_period_num}")
    
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is not None:
        prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
        prev_blue_ball = int(prev_row['b'])
        prev_red_str = ' '.join(map(str, prev_red_balls))
        print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
        
        # 计算重号
        current_red_balls = latest_period['red_balls']
        repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
        manual_repeat_count = len(repeat_balls)
        
        print(f"当前期红球: {current_red_balls}")
        print(f"上一期红球: {prev_red_balls}")
        print(f"重号球: {repeat_balls}")
        print(f"手动计算重号数: {manual_repeat_count}")
        
        # 测试系统方法
        system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
        print(f"系统计算重号数: {system_repeat_count}")
        
        if manual_repeat_count == system_repeat_count == 1:
            print("✅ 重号数计算正确")
            return True
        elif manual_repeat_count == system_repeat_count == 0:
            print("⚠️ 重号数为0（可能是正确的）")
            return True
        else:
            print("❌ 重号数计算错误")
            return False
    else:
        print("❌ 未找到上一期数据")
        return False


def run_full_analysis_simulation(target_period):
    """运行完整的分析比对模拟"""
    print(f"\n=== 运行完整的分析比对模拟 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 获取分析期号列表
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    print(f"目标期号: {target_period}")
    print(f"总分析期数: {len(analysis_periods)}")
    
    # 模拟第50期分析
    if len(analysis_periods) >= 50:
        period_50 = analysis_periods[49]  # 第50期（索引49）
        
        print(f"第50期分析期号: {period_50}")
        
        # 获取数据
        current_database = system.data_loader.get_database_for_period(period_50, 99)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 获取冷球信息
        pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
        analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
        
        # 获取筛选要求
        filter_requirements = system.prediction_engine.filter_requirements
        
        # 计算重号数
        repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
        
        print(f"计算的重号数: {repeat_count}")
        
        # 显示进度
        print(f"\n=== 第50期进度显示 ===")
        system.ui.show_analysis_progress(
            50, len(analysis_periods),
            len(current_database), latest_period,
            predictions, filter_requirements,
            (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
            (analysis_red_cold_balls, analysis_blue_cold_balls)
        )
        
        return repeat_count
    
    return None


def main():
    """主函数"""
    print("查找用户实际运行场景")
    print("=" * 60)
    
    # 查找用户的目标期号
    target_period, period_50 = find_target_period_for_user_scenario()
    
    if target_period is not None:
        # 测试重号数计算
        success = test_user_scenario_repeat_count(target_period, period_50)
        
        # 运行完整模拟
        repeat_count = run_full_analysis_simulation(target_period)
        
        print("\n" + "=" * 60)
        if success and repeat_count == 1:
            print("🎉 找到用户场景，重号数计算正确！")
            return True
        elif repeat_count == 0:
            print("⚠️ 重号数为0，可能是正确的（该期确实没有重号）")
            return True
        else:
            print("❌ 重号数计算仍有问题")
            return False
    else:
        print("❌ 未找到用户的实际运行场景")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
