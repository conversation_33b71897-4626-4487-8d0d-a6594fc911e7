# -*- coding: utf-8 -*-
"""
用户界面模块 (User Interface Module)

负责处理用户交互、输入验证和结果显示。
"""

import re
from typing import Dict, List, Optional


class UserInterface:
    """
    用户界面类
    
    负责所有用户交互功能
    """
    
    def __init__(self):
        """初始化用户界面"""
        pass
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("=" * 60)
        print("          双色球彩票预测与分析系统 V1.0")
        print("=" * 60)
        print("本系统提供24种预测算法，包括：")
        print("- 历史出现概率分析")
        print("- 马尔科夫链算法")
        print("- 贝叶斯概率算法")
        print("- 多种筛选策略组合")
        print("=" * 60)
    
    def get_main_menu_choice(self) -> int:
        """获取主菜单选择"""
        while True:
            print("\n请选择功能：")
            print("1. 预测选号")
            print("2. 分析比对")
            print("0. 退出程序")
            
            try:
                choice = input("请输入选择 (0-2): ").strip()
                choice_int = int(choice)
                
                if choice_int in [0, 1, 2]:
                    return choice_int
                else:
                    print("无效选择，请输入 0、1 或 2。")
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def get_database_range(self) -> int:
        """获取数据库范围"""
        while True:
            print("\n请指定数据库范围：")
            print("输入 0 表示使用全部历史数据")
            print("输入正整数表示使用最近N期数据（如：500表示最近500期）")
            
            try:
                range_input = input("请输入数据范围: ").strip()
                range_int = int(range_input)
                
                if range_int >= 0:
                    return range_int
                else:
                    print("请输入非负整数。")
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def get_target_period(self) -> str:
        """获取目标期号"""
        while True:
            print("\n请输入要开始分析的期号：")
            print("期号格式：5位数字，如 25001 表示2025年第1期")
            
            period_input = input("请输入期号: ").strip()
            
            # 验证期号格式
            if re.match(r'^\d{5}$', period_input):
                return period_input
            else:
                print("期号格式错误，请输入5位数字。")
    
    def show_latest_period_info(self, latest_period: Dict, database):
        """显示最新一期信息"""
        if latest_period is None:
            print("无法获取最新一期信息。")
            return
        
        print(f"\n=== 最新一期信息 ===")
        red_balls_str = ' '.join(map(str, latest_period['red_balls']))
        print(f"最新一期的号码为：{latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")
        
        # 显示数据库信息
        print(f"当前数据库包含 {len(database)} 期数据")
        # 修正数据范围显示 - 确保按正确顺序显示起始和结束期号
        start_period = min(database['NO'])
        end_period = max(database['NO'])
        print(f"数据范围：{start_period} - {end_period}")
    
    def show_cold_ball_info(self, red_cold_balls: List[int], blue_cold_balls: List[int]):
        """显示冷球信息"""
        if red_cold_balls:
            red_cold_str = ' '.join(map(str, red_cold_balls))
            print(f"红球冷球号码为：{red_cold_str}")
        else:
            print("红球冷球号码为：无")
        
        if blue_cold_balls:
            blue_cold_str = ' '.join(map(str, blue_cold_balls))
            print(f"蓝球冷球号码为：{blue_cold_str}")
        else:
            print("蓝球冷球号码为：无")
    
    def show_prediction_requirements(self, requirements: Dict):
        """显示预测要求信息"""
        print(f"\n=== 预测要求 ===")
        print(f"红球大球数要求：{requirements.get('red_big_ball_count', 0)}")
        print(f"蓝球大球数要求：{requirements.get('blue_big_ball_count', 0)}")
        print(f"红球冷球数要求：{requirements.get('red_cold_ball_count', 0)}")
        print(f"蓝球冷球数要求：{requirements.get('blue_cold_ball_count', 0)}")
        print(f"红球重号数要求：{requirements.get('red_repeat_count', 0)}")
    
    def show_prediction_results(self, predictions: Dict, filter_requirements: Dict = None, cold_balls: tuple = None, latest_period: Dict = None):
        """显示预测结果"""
        print(f"\n=== 预测结果 ===")

        for group_id in sorted(predictions.keys()):
            prediction = predictions[group_id]
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            print(f"第{group_id}组预测的号码为：{prediction['method']} {red_balls_str} + {prediction['blue_ball']}")

            # 为第4、5、6组添加详细信息
            if filter_requirements and group_id in [4, 5, 6]:
                self._show_prediction_details(group_id, prediction, filter_requirements, cold_balls, latest_period)

    def _show_prediction_details(self, group_id: int, prediction: Dict, filter_requirements: Dict, cold_balls: tuple = None, latest_period: Dict = None):
        """显示预测详细信息"""
        red_balls = prediction['red_balls']
        blue_ball = prediction['blue_ball']

        if group_id == 4:  # 大球筛选
            # 计算实际大球数
            red_big_count = sum(1 for ball in red_balls if ball > 16)
            blue_big_count = 1 if blue_ball > 8 else 0
            required_red_big = filter_requirements.get('red_big_ball_count', 0)
            required_blue_big = filter_requirements.get('blue_big_ball_count', 0)
            print(f"    要求的红球大球数：{required_red_big}，实际：{red_big_count}")
            print(f"    要求的蓝球大球数：{required_blue_big}，实际：{blue_big_count}")

        elif group_id == 5:  # 冷球筛选
            required_red_cold = filter_requirements.get('red_cold_ball_count', 0)
            required_blue_cold = filter_requirements.get('blue_cold_ball_count', 0)

            # 计算实际冷球数
            if cold_balls:
                red_cold_balls, blue_cold_balls = cold_balls
                actual_red_cold = sum(1 for ball in red_balls if ball in red_cold_balls)
                actual_blue_cold = 1 if blue_ball in blue_cold_balls else 0
                print(f"    要求的红球冷球数：{required_red_cold}，实际：{actual_red_cold}")
                print(f"    要求的蓝球冷球数：{required_blue_cold}，实际：{actual_blue_cold}")
            else:
                print(f"    要求的红球冷球数：{required_red_cold}")
                print(f"    要求的蓝球冷球数：{required_blue_cold}")

        elif group_id == 6:  # 重号筛选
            required_red_repeat = filter_requirements.get('red_repeat_count', 0)

            # 计算实际重号数
            if latest_period:
                latest_red_balls = latest_period['red_balls']
                actual_red_repeat = sum(1 for ball in red_balls if ball in latest_red_balls)
                print(f"    要求的红球重号数：{required_red_repeat}，实际：{actual_red_repeat}")
            else:
                print(f"    要求的红球重号数：{required_red_repeat}")
    
    def ask_save_results(self) -> bool:
        """询问是否保存结果"""
        while True:
            choice = input("\n是否保存预测结果到Excel文件？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n。")
    
    def show_analysis_progress(self, completed: int, total: int, database_size: int, latest_period: Dict,
                             predictions: Dict = None, filter_requirements: Dict = None,
                             cold_balls: tuple = None, repeat_count: int = 0):
        """显示分析进度"""
        progress_percent = (completed / total) * 100 if total > 0 else 0

        print(f"\n=== 分析进度 ===")
        print(f"已完成：{completed}/{total} 期 ({progress_percent:.1f}%)")
        print(f"当前数据库包含：{database_size} 期数据")

        if latest_period:
            red_balls_str = ' '.join(map(str, latest_period['red_balls']))
            print(f"当前最新1期的号码：{latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")

            # 显示最新1期号码的大球数
            red_big_count = sum(1 for ball in latest_period['red_balls'] if ball > 16)
            blue_big_count = 1 if latest_period['blue_ball'] > 8 else 0
            print(f"最新1期号码的红球大球数：{red_big_count}")
            print(f"最新1期号码的蓝球大球数：{blue_big_count}")

            # 显示最新1期号码的冷球数
            if cold_balls:
                red_cold_balls, blue_cold_balls = cold_balls
                latest_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in red_cold_balls)
                latest_blue_cold_count = 1 if latest_period['blue_ball'] in blue_cold_balls else 0
                print(f"最新1期号码的红球冷球数：{latest_red_cold_count}")
                print(f"最新1期号码的蓝球冷球数：{latest_blue_cold_count}")

            # 显示最新1期号码的重号数（与上期比较）
            print(f"最新1期号码的红球重号数：{repeat_count}")

            # 显示冷球号码
            if cold_balls:
                red_cold_balls, blue_cold_balls = cold_balls
                if red_cold_balls:
                    red_cold_str = ' '.join(map(str, red_cold_balls))
                    print(f"基于被定义的当前数据库（最近5期红蓝球号码）识别出来的红球冷球号码：{red_cold_str}")
                else:
                    print(f"基于被定义的当前数据库（最近5期红蓝球号码）识别出来的红球冷球号码：无")

                if blue_cold_balls:
                    blue_cold_str = ' '.join(map(str, blue_cold_balls))
                    print(f"基于被定义的当前数据库（最近5期红蓝球号码）识别出来的蓝球冷球号码：{blue_cold_str}")
                else:
                    print(f"基于被定义的当前数据库（最近5期红蓝球号码）识别出来的蓝球冷球号码：无")

            # 显示预测要求信息
            if predictions and filter_requirements:
                self._show_prediction_requirements_in_progress(predictions, filter_requirements, cold_balls, latest_period)

    def _show_prediction_requirements_in_progress(self, predictions: Dict, filter_requirements: Dict,
                                                cold_balls: tuple, latest_period: Dict):
        """在进度显示中显示预测要求信息"""
        # 显示第4组预测要求（大球筛选）
        if 4 in predictions:
            prediction_4 = predictions[4]
            red_balls_4 = prediction_4['red_balls']
            blue_ball_4 = prediction_4['blue_ball']

            # 计算实际大球数
            actual_red_big = sum(1 for ball in red_balls_4 if ball > 16)
            actual_blue_big = 1 if blue_ball_4 > 8 else 0
            required_red_big = filter_requirements.get('red_big_ball_count', 0)
            required_blue_big = filter_requirements.get('blue_big_ball_count', 0)

            print(f"预测的号码（第4组）中要求的红球大球数：{required_red_big}")
            print(f"预测的号码（第4组）中要求的蓝球大球数：{required_blue_big}")

        # 显示第5组预测要求（冷球筛选）
        if 5 in predictions:
            required_red_cold = filter_requirements.get('red_cold_ball_count', 0)
            required_blue_cold = filter_requirements.get('blue_cold_ball_count', 0)

            print(f"预测的号码（第5组）中要求的红球冷球数：{required_red_cold}")
            print(f"预测的号码（第5组）中要求的蓝球冷球数：{required_blue_cold}")

        # 显示第6组预测要求（重号筛选）
        if 6 in predictions:
            required_red_repeat = filter_requirements.get('red_repeat_count', 0)
            print(f"预测的号码（第6组）中要求的红球重号数：{required_red_repeat}")
    
    def show_final_analysis_results(self, results: List[Dict]):
        """显示最终分析结果"""
        if not results:
            print("没有分析结果。")
            return
        
        print(f"\n=== 最终分析结果 ===")
        print(f"总共分析了 {len(results)} 期数据")
        
        # 统计每组预测的命中分布
        hit_distribution = {}
        for i in range(25):  # 1-24组
            hit_distribution[i] = {j: 0 for j in range(8)}  # 0-7球命中
        
        for result in results:
            comparison = result['comparison']
            for group_id, group_result in comparison.items():
                max_hits = group_result['max_hit']['total_hits']
                hit_distribution[group_id][max_hits] += 1
        
        # 显示统计结果
        print(f"\n各组预测最大命中情况分布统计：")
        print("组号\t方法\t\t\t0球\t1球\t2球\t3球\t4球\t5球\t6球\t7球")
        print("-" * 80)
        
        for group_id in range(1, 25):
            if group_id in hit_distribution:
                # 获取方法名（从第一个结果中获取）
                method_name = "未知方法"
                if results and group_id in results[0]['predictions']:
                    method_name = results[0]['predictions'][group_id]['method']
                    if len(method_name) > 12:
                        method_name = method_name[:12] + "..."
                
                dist = hit_distribution[group_id]
                print(f"{group_id}\t{method_name:<15}\t{dist[0]}\t{dist[1]}\t{dist[2]}\t{dist[3]}\t{dist[4]}\t{dist[5]}\t{dist[6]}\t{dist[7]}")
    
    def show_error_message(self, message: str):
        """显示错误信息"""
        print(f"\n错误: {message}")
    
    def show_info_message(self, message: str):
        """显示提示信息"""
        print(f"\n提示: {message}")
    
    def show_success_message(self, message: str):
        """显示成功信息"""
        print(f"\n成功: {message}")
    
    def confirm_action(self, message: str) -> bool:
        """确认操作"""
        while True:
            choice = input(f"\n{message} (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n。")
    
    def wait_for_user(self):
        """等待用户按键"""
        input("\n按回车键继续...")
    
    def show_separator(self, char: str = "=", length: int = 60):
        """显示分隔线"""
        print(char * length)
