#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析比对结果的导出准确性
验证导出的Excel文件中的数据是否正确
"""

import sys
import pandas as pd
import os
from ssq_lottery_system import SSQLotterySystem


def test_analysis_export():
    """测试分析比对结果导出"""
    print("=== 测试分析比对结果导出 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定期号进行测试
    target_period = '25029'
    database_range = 200
    
    print(f"测试期号: {target_period}")
    print(f"数据范围: {database_range} 期")
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("❌ 无法获取答案数据")
        return False
    
    print(f"✓ 当前数据库: {len(current_database)} 期")
    print(f"✓ 答案数据: {len(answer_data)} 期")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 进行比对
    comparison_results = system.comparison_engine.compare_predictions(predictions, answer_data)
    
    # 构建分析结果
    results = [{
        'period': target_period,
        'predictions': predictions,
        'comparison': comparison_results,
        'database_size': len(current_database),
        'latest_period': latest_period
    }]
    
    # 导出分析结果
    print(f"\n=== 导出分析结果 ===")
    system.export_manager.export_analysis_results(results)
    
    # 查找最新的分析结果文件
    output_dir = "output"
    analysis_files = [f for f in os.listdir(output_dir) if f.startswith('analysis_results_') and f.endswith('.xlsx')]
    
    if not analysis_files:
        print("❌ 没有找到分析结果文件")
        return False
    
    latest_file = max(analysis_files)
    file_path = os.path.join(output_dir, latest_file)
    
    print(f"✓ 分析结果文件: {latest_file}")
    
    # 读取并验证导出的数据
    print(f"\n=== 验证导出数据 ===")
    
    try:
        # 读取详细比对结果
        df_detailed = pd.read_excel(file_path, sheet_name='详细比对结果')
        print(f"✓ 详细比对结果: {len(df_detailed)} 行数据")
        
        # 验证前几组的数据
        for group_id in range(1, 7):
            group_data = df_detailed[df_detailed['预测组号'] == group_id]
            
            if len(group_data) == 0:
                print(f"❌ 第{group_id}组数据缺失")
                continue
            
            row = group_data.iloc[0]
            
            # 获取对应的预测和比对结果
            prediction = predictions[group_id]
            group_result = comparison_results[group_id]
            max_hit = group_result['max_hit']
            
            # 验证数据
            pred_red_str = ' '.join(map(str, prediction['red_balls']))
            
            print(f"\n第{group_id}组验证:")
            print(f"  预测方法: {row['预测方法']} (预期: {prediction['method']})")
            print(f"  预测红球: {row['预测红球']} (预期: {pred_red_str})")
            print(f"  预测蓝球: {row['预测蓝球']} (预期: {prediction['blue_ball']})")
            print(f"  最大命中球数: {row['最大命中球数']} (预期: {max_hit['total_hits']})")
            print(f"  最大命中期号: {row['最大命中期号']} (预期: {max_hit['period']})")
            print(f"  红球命中数: {row['红球命中数']} (预期: {max_hit['red_hits']})")
            print(f"  蓝球命中数: {row['蓝球命中数']} (预期: {max_hit['blue_hits']})")
            print(f"  蓝球命中状态: {row['蓝球命中状态']} (预期: {'是' if max_hit['blue_hits'] == 1 else '否'})")
            
            # 检查是否有不匹配
            errors = []
            if row['预测方法'] != prediction['method']:
                errors.append("预测方法不匹配")
            if row['预测红球'] != pred_red_str:
                errors.append("预测红球不匹配")
            if row['预测蓝球'] != prediction['blue_ball']:
                errors.append("预测蓝球不匹配")
            if row['最大命中球数'] != max_hit['total_hits']:
                errors.append("最大命中球数不匹配")
            if row['最大命中期号'] != max_hit['period']:
                errors.append("最大命中期号不匹配")
            if row['红球命中数'] != max_hit['red_hits']:
                errors.append("红球命中数不匹配")
            if row['蓝球命中数'] != max_hit['blue_hits']:
                errors.append("蓝球命中数不匹配")
            
            expected_blue_status = '是' if max_hit['blue_hits'] == 1 else '否'
            if row['蓝球命中状态'] != expected_blue_status:
                errors.append("蓝球命中状态不匹配")
            
            if errors:
                print(f"  ❌ 发现错误: {', '.join(errors)}")
            else:
                print(f"  ✅ 数据验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取分析结果文件失败: {e}")
        return False


def test_manual_verification():
    """手动验证特定数据"""
    print(f"\n=== 手动验证特定数据 ===")
    
    # 模拟一个预测结果
    prediction = {
        'method': '历史出现概率',
        'red_balls': [7, 11, 17, 18, 20, 22],
        'blue_ball': 15
    }
    
    # 模拟答案数据
    answer_data = [
        {'period': '25030', 'red_balls': [4, 6, 7, 30, 31, 33], 'blue_ball': 6},
        {'period': '25031', 'red_balls': [1, 5, 6, 8, 23, 28], 'blue_ball': 1},
        {'period': '25032', 'red_balls': [3, 8, 10, 14, 16, 21], 'blue_ball': 3},
        {'period': '25033', 'red_balls': [3, 5, 18, 25, 26, 33], 'blue_ball': 8},
        {'period': '25034', 'red_balls': [5, 8, 13, 14, 24, 26], 'blue_ball': 12},
        {'period': '25035', 'red_balls': [1, 8, 16, 18, 25, 31], 'blue_ball': 1}
    ]
    
    print(f"测试预测: {' '.join(map(str, prediction['red_balls']))} + {prediction['blue_ball']}")
    
    # 手动计算比对结果
    pred_red = set(prediction['red_balls'])
    pred_blue = prediction['blue_ball']
    
    max_total_hits = 0
    max_period = ""
    max_red_hits = 0
    max_blue_hits = 0
    
    for answer in answer_data:
        answer_red = set(answer['red_balls'])
        answer_blue = answer['blue_ball']
        
        red_hits = len(pred_red & answer_red)
        blue_hits = 1 if pred_blue == answer_blue else 0
        total_hits = red_hits + blue_hits
        
        if total_hits > max_total_hits:
            max_total_hits = total_hits
            max_period = answer['period']
            max_red_hits = red_hits
            max_blue_hits = blue_hits
        
        answer_red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  期号{answer['period']}: {answer_red_str} + {answer_blue} -> 红{red_hits}+蓝{blue_hits}={total_hits}")
    
    print(f"\n最大命中结果:")
    print(f"  最大命中球数: {max_total_hits}")
    print(f"  最大命中期号: {max_period}")
    print(f"  红球命中数: {max_red_hits}")
    print(f"  蓝球命中数: {max_blue_hits}")
    print(f"  蓝球命中状态: {'是' if max_blue_hits == 1 else '否'}")
    
    return True


def main():
    """主测试函数"""
    print("分析比对结果导出准确性测试")
    print("=" * 60)
    
    success1 = test_analysis_export()
    success2 = test_manual_verification()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 分析比对结果导出准确性测试通过！")
        return True
    else:
        print("⚠️ 发现导出数据问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
