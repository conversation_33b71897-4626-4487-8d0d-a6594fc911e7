#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试用户截图中的具体案例
验证25067期第9组的比对结果
"""

import sys
from modules.comparison_engine import ComparisonEngine


def debug_specific_case():
    """调试用户截图中的具体案例"""
    print("=== 调试用户截图中的具体案例 ===")
    
    # 用户截图中的数据
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '9号小冷大球红球重号筛选'
    }
    
    # 25067期的答案数据（基于实际数据）
    answer_data = [
        {'period': '25068', 'red_balls': [5, 7, 8, 19, 20, 31], 'blue_ball': 7},
        {'period': '25069', 'red_balls': [2, 4, 19, 23, 27, 30], 'blue_ball': 5},
        {'period': '25070', 'red_balls': [2, 3, 15, 21, 22, 33], 'blue_ball': 6},
        {'period': '25071', 'red_balls': [1, 12, 18, 23, 25, 28], 'blue_ball': 7},
        {'period': '25072', 'red_balls': [2, 14, 17, 25, 27, 29], 'blue_ball': 5},
        {'period': '25073', 'red_balls': [2, 7, 10, 27, 30, 33], 'blue_ball': 11}
    ]
    
    print("用户截图数据:")
    print(f"  预测红球: {prediction['red_balls']}")
    print(f"  预测蓝球: {prediction['blue_ball']}")
    print(f"  预测方法: {prediction['method']}")
    
    print(f"\n答案数据:")
    for answer in answer_data:
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  期号{answer['period']}: {red_str} + {answer['blue_ball']}")
    
    # 手动计算每期的命中情况
    print(f"\n手动计算每期命中情况:")
    pred_red_set = set(prediction['red_balls'])
    pred_blue = prediction['blue_ball']
    
    max_total_hits = 0
    max_hit_period = None
    max_hit_details = None
    
    for answer in answer_data:
        answer_red_set = set(answer['red_balls'])
        answer_blue = answer['blue_ball']
        
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if pred_blue == answer_blue else 0
        total_hits = red_hits + blue_hits
        
        hit_red_balls = sorted(list(pred_red_set & answer_red_set))
        hit_red_str = ' '.join(map(str, hit_red_balls))
        
        print(f"  期号{answer['period']}:")
        print(f"    红球交集: {hit_red_balls}")
        print(f"    红球命中: {red_hits}个")
        print(f"    蓝球命中: {blue_hits}个 (预测{pred_blue} vs 实际{answer_blue})")
        print(f"    总命中: {total_hits}球")
        
        if total_hits > max_total_hits:
            max_total_hits = total_hits
            max_hit_period = answer['period']
            max_hit_details = {
                'period': answer['period'],
                'red_hits': red_hits,
                'blue_hits': blue_hits,
                'total_hits': total_hits,
                'blue_hit_status': blue_hits == 1,
                'answer_red_balls': answer['red_balls'],
                'answer_blue_ball': answer['blue_ball'],
                'hit_red_balls': hit_red_balls
            }
        print()
    
    print(f"手动计算结果:")
    print(f"  最大命中期号: {max_hit_period}")
    print(f"  最大命中球数: {max_total_hits}")
    print(f"  红球命中数: {max_hit_details['red_hits']}")
    print(f"  蓝球命中数: {max_hit_details['blue_hits']}")
    print(f"  蓝球命中状态: {'是' if max_hit_details['blue_hit_status'] else '否'}")
    print(f"  命中红球: {max_hit_details['hit_red_balls']}")
    
    # 使用比对引擎验证
    print(f"\n使用比对引擎验证:")
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {9: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 9 in comparison_results:
        group_result = comparison_results[9]
        max_hit = group_result['max_hit']
        
        print(f"  比对引擎结果:")
        print(f"    最大命中期号: {max_hit['period']}")
        print(f"    最大命中球数: {max_hit['total_hits']}")
        print(f"    红球命中数: {max_hit['red_hits']}")
        print(f"    蓝球命中数: {max_hit['blue_hits']}")
        print(f"    蓝球命中状态: {'是' if max_hit['blue_hit_status'] else '否'}")
        print(f"    答案红球: {max_hit['answer_red_balls']}")
        print(f"    答案蓝球: {max_hit['answer_blue_ball']}")
        
        # 验证一致性
        if (max_hit['period'] == max_hit_details['period'] and
            max_hit['total_hits'] == max_hit_details['total_hits'] and
            max_hit['red_hits'] == max_hit_details['red_hits'] and
            max_hit['blue_hits'] == max_hit_details['blue_hits'] and
            max_hit['blue_hit_status'] == max_hit_details['blue_hit_status']):
            print(f"\n  ✅ 比对引擎计算正确")
            
            # 检查用户截图显示的问题
            print(f"\n用户截图显示的问题分析:")
            print(f"  截图显示: 红5+蓝1=6球, 蓝球命中状态'是'")
            print(f"  正确应该: 红5+蓝0=5球, 蓝球命中状态'否'")
            print(f"  问题: 蓝球命中数和状态计算错误")
            
            return True
        else:
            print(f"\n  ❌ 比对引擎计算错误")
            print(f"    期望: 期号{max_hit_details['period']}, 红{max_hit_details['red_hits']}+蓝{max_hit_details['blue_hits']}={max_hit_details['total_hits']}球")
            print(f"    实际: 期号{max_hit['period']}, 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def test_blue_ball_calculation():
    """专门测试蓝球命中计算"""
    print(f"\n=== 专门测试蓝球命中计算 ===")
    
    test_cases = [
        {'pred': 14, 'actual': 11, 'expected': 0, 'desc': '预测14, 实际11'},
        {'pred': 14, 'actual': 14, 'expected': 1, 'desc': '预测14, 实际14'},
        {'pred': 11, 'actual': 11, 'expected': 1, 'desc': '预测11, 实际11'},
        {'pred': 7, 'actual': 11, 'expected': 0, 'desc': '预测7, 实际11'}
    ]
    
    all_correct = True
    
    for case in test_cases:
        pred_blue = case['pred']
        actual_blue = case['actual']
        expected = case['expected']
        
        # 模拟比对引擎的计算
        blue_hits = 1 if pred_blue == actual_blue else 0
        
        print(f"  {case['desc']}: 计算结果{blue_hits}, 期望{expected} - {'✅' if blue_hits == expected else '❌'}")
        
        if blue_hits != expected:
            all_correct = False
    
    return all_correct


def main():
    """主函数"""
    print("调试用户截图中的具体案例")
    print("=" * 60)
    
    success1 = debug_specific_case()
    success2 = test_blue_ball_calculation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎯 调试结论:")
        print("1. ✅ 比对引擎的计算逻辑是正确的")
        print("2. ✅ 蓝球命中计算逻辑是正确的")
        print("3. ❌ 用户截图显示的结果有误")
        print("\n问题分析:")
        print("- 预测蓝球14, 实际蓝球11, 应该是蓝球未命中")
        print("- 正确结果应该是: 红5+蓝0=5球")
        print("- 用户截图显示: 红5+蓝1=6球 (错误)")
        print("\n可能原因:")
        print("- 用户的Excel文件可能是旧版本生成的")
        print("- 或者数据传递过程中出现了问题")
        print("\n建议:")
        print("- 重新运行主程序生成最新的Excel文件")
        print("- 使用修正后的比对引擎")
    else:
        print("❌ 调试发现比对引擎存在问题")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
