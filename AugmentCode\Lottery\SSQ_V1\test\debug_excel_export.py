#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel导出
检查从比对引擎到Excel导出的完整数据流
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_excel_export():
    """调试Excel导出"""
    print("=== 调试Excel导出 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    try:
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        # 测试25067期
        test_period = '25067'
        if test_period not in analysis_periods:
            print(f"❌ 未找到期号 {test_period}")
            return False
        
        print(f"\n=== 调试期号 {test_period} ===")
        
        # 获取数据库和答案
        current_database = system.data_loader.get_database_for_period(test_period, database_range)
        answer_data = system.data_loader.get_answer_data(test_period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        print(f"数据库大小: {len(current_database)} 期")
        print(f"答案数据: {len(answer_data)} 期")
        print(f"最新期号: {latest_period}")
        
        # 显示答案数据
        print(f"\n答案数据:")
        for answer in answer_data:
            red_str = ' '.join(map(str, answer['red_balls']))
            print(f"  期号{answer['period']}: {red_str} + {answer['blue_ball']}")
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        print(f"\n生成的预测 (前5组):")
        for group_id in range(1, 6):
            if group_id in predictions:
                prediction = predictions[group_id]
                red_str = ' '.join(map(str, prediction['red_balls']))
                print(f"  第{group_id}组: {red_str} + {prediction['blue_ball']} ({prediction['method']})")
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        print(f"\n比对结果 (前5组):")
        for group_id in range(1, 6):
            if group_id in comparison_result:
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                print(f"  第{group_id}组:")
                print(f"    最大命中期号: {max_hit['period']}")
                print(f"    最大命中球数: {max_hit['total_hits']}")
                print(f"    红球命中数: {max_hit['red_hits']}")
                print(f"    蓝球命中数: {max_hit['blue_hits']}")
                print(f"    蓝球命中状态: {max_hit['blue_hit_status']}")
                print(f"    答案红球: {max_hit['answer_red_balls']}")
                print(f"    答案蓝球: {max_hit['answer_blue_ball']}")
        
        # 手动验证一个具体案例
        print(f"\n=== 手动验证案例 ===")
        
        # 查找预测号码为 [2, 7, 10, 22, 27, 33] + 14 的组
        target_red = [2, 7, 10, 22, 27, 33]
        target_blue = 14
        
        matching_groups = []
        for group_id, prediction in predictions.items():
            pred_red = [int(x) for x in prediction['red_balls']]
            pred_blue = int(prediction['blue_ball'])
            
            if pred_red == target_red and pred_blue == target_blue:
                matching_groups.append(group_id)
        
        if matching_groups:
            print(f"找到匹配的预测组: {matching_groups}")
            
            for group_id in matching_groups:
                print(f"\n第{group_id}组详细验证:")
                prediction = predictions[group_id]
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                print(f"  预测: {target_red} + {target_blue}")
                print(f"  最大命中期号: {max_hit['period']}")
                print(f"  答案: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
                
                # 手动计算
                pred_red_set = set(target_red)
                answer_red_set = set(max_hit['answer_red_balls'])
                red_intersection = pred_red_set & answer_red_set
                manual_red_hits = len(red_intersection)
                manual_blue_hits = 1 if target_blue == max_hit['answer_blue_ball'] else 0
                manual_total_hits = manual_red_hits + manual_blue_hits
                
                print(f"  手动计算:")
                print(f"    红球交集: {red_intersection}")
                print(f"    红球命中数: {manual_red_hits}")
                print(f"    蓝球命中数: {manual_blue_hits}")
                print(f"    总命中数: {manual_total_hits}")
                print(f"    蓝球命中状态: {'是' if manual_blue_hits == 1 else '否'}")
                
                print(f"  比对引擎结果:")
                print(f"    红球命中数: {max_hit['red_hits']}")
                print(f"    蓝球命中数: {max_hit['blue_hits']}")
                print(f"    总命中数: {max_hit['total_hits']}")
                print(f"    蓝球命中状态: {'是' if max_hit['blue_hit_status'] else '否'}")
                
                # 验证一致性
                if (max_hit['red_hits'] == manual_red_hits and
                    max_hit['blue_hits'] == manual_blue_hits and
                    max_hit['total_hits'] == manual_total_hits and
                    max_hit['blue_hit_status'] == (manual_blue_hits == 1)):
                    print(f"    ✅ 比对引擎计算正确")
                else:
                    print(f"    ❌ 比对引擎计算错误")
                    return False
        else:
            print(f"❌ 未找到预测号码 {target_red} + {target_blue} 的组")
            print(f"实际生成的预测:")
            for group_id in range(1, 10):
                if group_id in predictions:
                    prediction = predictions[group_id]
                    pred_red = [int(x) for x in prediction['red_balls']]
                    pred_blue = int(prediction['blue_ball'])
                    print(f"  第{group_id}组: {pred_red} + {pred_blue}")
        
        # 模拟Excel导出数据
        print(f"\n=== 模拟Excel导出数据 ===")
        
        detailed_data = []
        for group_id in range(1, 6):
            if group_id in predictions and group_id in comparison_result:
                prediction = predictions[group_id]
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                red_balls_str = ' '.join(map(str, prediction['red_balls']))
                
                excel_row = {
                    '分析期号': test_period,
                    '预测组号': group_id,
                    '预测方法': prediction['method'],
                    '预测红球': red_balls_str,
                    '预测蓝球': prediction['blue_ball'],
                    '最大命中球数': max_hit['total_hits'],
                    '最大命中期号': max_hit['period'],
                    '红球命中数': max_hit['red_hits'],
                    '蓝球命中数': max_hit['blue_hits'],
                    '蓝球命中状态': '是' if max_hit['blue_hits'] == 1 else '否'
                }
                
                detailed_data.append(excel_row)
                
                print(f"第{group_id}组Excel行:")
                for key, value in excel_row.items():
                    print(f"  {key}: {value}")
                print()
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("调试Excel导出")
    print("=" * 60)
    
    success = debug_excel_export()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 调试完成！")
        print("如果比对引擎计算正确，但Excel仍显示错误，")
        print("可能需要检查Excel导出代码或重新生成Excel文件")
    else:
        print("❌ 调试发现问题")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
