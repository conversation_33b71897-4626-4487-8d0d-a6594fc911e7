#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最大命中选择逻辑
"""

import sys


def test_max_selection_logic():
    """测试最大命中选择逻辑"""
    print("=== 测试最大命中选择逻辑 ===")
    
    # 模拟比对结果
    group_results = [
        {'period': '25002', 'total_hits': 0, 'red_hits': 0, 'blue_hits': 0},
        {'period': '25003', 'total_hits': 0, 'red_hits': 0, 'blue_hits': 0},
        {'period': '25004', 'total_hits': 1, 'red_hits': 1, 'blue_hits': 0},
        {'period': '25005', 'total_hits': 0, 'red_hits': 0, 'blue_hits': 0},
        {'period': '25006', 'total_hits': 1, 'red_hits': 1, 'blue_hits': 0},
        {'period': '25007', 'total_hits': 0, 'red_hits': 0, 'blue_hits': 0}
    ]
    
    print("比对结果:")
    for result in group_results:
        print(f"  期号{result['period']}: {result['total_hits']}球")
    
    # 当前的选择逻辑
    max_hit_current = max(group_results, key=lambda x: (x['total_hits'], int(x['period'])))
    print(f"\n当前逻辑选择: 期号{max_hit_current['period']}, {max_hit_current['total_hits']}球")
    
    # 手动验证
    max_hits = max(result['total_hits'] for result in group_results)
    candidates = [result for result in group_results if result['total_hits'] == max_hits]
    
    print(f"\n最大命中数: {max_hits}")
    print(f"候选期号: {[c['period'] for c in candidates]}")
    
    # 按期号排序，选择最大的
    max_period_candidate = max(candidates, key=lambda x: int(x['period']))
    print(f"应该选择: 期号{max_period_candidate['period']}")
    
    if max_hit_current['period'] == max_period_candidate['period']:
        print("✅ 选择逻辑正确")
        return True
    else:
        print("❌ 选择逻辑错误")
        return False


def test_different_scenarios():
    """测试不同场景的选择逻辑"""
    print(f"\n=== 测试不同场景的选择逻辑 ===")
    
    scenarios = [
        {
            'name': '单一最大值',
            'results': [
                {'period': '25001', 'total_hits': 1},
                {'period': '25002', 'total_hits': 3},
                {'period': '25003', 'total_hits': 2}
            ],
            'expected': '25002'
        },
        {
            'name': '多个相同最大值',
            'results': [
                {'period': '25001', 'total_hits': 2},
                {'period': '25002', 'total_hits': 3},
                {'period': '25003', 'total_hits': 3}
            ],
            'expected': '25003'
        },
        {
            'name': '全部相同',
            'results': [
                {'period': '25001', 'total_hits': 1},
                {'period': '25002', 'total_hits': 1},
                {'period': '25003', 'total_hits': 1}
            ],
            'expected': '25003'
        }
    ]
    
    all_passed = True
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        results = scenario['results']
        expected = scenario['expected']
        
        # 当前逻辑
        max_hit = max(results, key=lambda x: (x['total_hits'], int(x['period'])))
        actual = max_hit['period']
        
        print(f"结果: {[(r['period'], r['total_hits']) for r in results]}")
        print(f"期望选择: {expected}")
        print(f"实际选择: {actual}")
        
        if actual == expected:
            print("✅ 通过")
        else:
            print("❌ 失败")
            all_passed = False
    
    return all_passed


def test_real_case():
    """测试真实案例"""
    print(f"\n=== 测试真实案例 ===")
    
    # 用户案例的实际比对结果
    real_results = [
        {'period': '25068', 'total_hits': 1, 'red_hits': 1, 'blue_hits': 0},
        {'period': '25069', 'total_hits': 2, 'red_hits': 2, 'blue_hits': 0},
        {'period': '25070', 'total_hits': 3, 'red_hits': 3, 'blue_hits': 0},
        {'period': '25071', 'total_hits': 0, 'red_hits': 0, 'blue_hits': 0},
        {'period': '25072', 'total_hits': 2, 'red_hits': 2, 'blue_hits': 0},
        {'period': '25073', 'total_hits': 5, 'red_hits': 5, 'blue_hits': 0}
    ]
    
    print("用户案例比对结果:")
    for result in real_results:
        print(f"  期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']} = {result['total_hits']}球")
    
    # 选择最大命中
    max_hit = max(real_results, key=lambda x: (x['total_hits'], int(x['period'])))
    
    print(f"\n选择结果:")
    print(f"  最大命中: {max_hit['total_hits']}球")
    print(f"  最大命中期号: {max_hit['period']}")
    
    if max_hit['period'] == '25073' and max_hit['total_hits'] == 5:
        print("✅ 真实案例选择正确")
        return True
    else:
        print("❌ 真实案例选择错误")
        return False


def main():
    """主函数"""
    print("测试最大命中选择逻辑")
    print("=" * 60)
    
    success1 = test_max_selection_logic()
    success2 = test_different_scenarios()
    success3 = test_real_case()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 最大命中选择逻辑测试通过！")
    else:
        print("❌ 最大命中选择逻辑有问题")
    
    return success1 and success2 and success3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
