#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动比对验证工具
允许用户手动输入预测号码进行比对验证
"""

import sys
from modules.comparison_engine import ComparisonEngine
from modules.data_loader import DataLoader


def manual_comparison_tool():
    """手动比对验证工具"""
    print("=== 手动比对验证工具 ===")
    print("用于验证任意预测号码与实际开奖结果的比对")
    
    # 用户案例的数据
    print("\n用户案例:")
    print("预测号码: 2 7 10 22 27 33 + 14")
    print("实际号码: 2 7 10 27 30 33 + 11 (25073期)")
    print("期望结果: 5球命中 (红球5个，蓝球0个)")
    
    # 手动验证
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '用户案例'
    }
    
    answer_data = [{
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }]
    
    print(f"\n=== 手动验证计算 ===")
    
    # 手动计算
    pred_red_set = set(prediction['red_balls'])
    answer_red_set = set(answer_data[0]['red_balls'])
    
    red_hits_manual = len(pred_red_set & answer_red_set)
    blue_hits_manual = 1 if prediction['blue_ball'] == answer_data[0]['blue_ball'] else 0
    total_hits_manual = red_hits_manual + blue_hits_manual
    
    print(f"预测红球集合: {pred_red_set}")
    print(f"实际红球集合: {answer_red_set}")
    print(f"红球交集: {pred_red_set & answer_red_set}")
    print(f"红球命中数: {red_hits_manual}")
    print(f"蓝球命中数: {blue_hits_manual}")
    print(f"总命中数: {total_hits_manual}")
    
    # 使用比对引擎验证
    print(f"\n=== 比对引擎验证 ===")
    
    config = {}
    engine = ComparisonEngine(config)
    results = engine._compare_single_prediction(prediction, answer_data)
    
    if results:
        result = results[0]
        print(f"比对引擎计算:")
        print(f"  红球命中数: {result['red_hits']}")
        print(f"  蓝球命中数: {result['blue_hits']}")
        print(f"  总命中数: {result['total_hits']}")
        
        # 验证一致性
        if (result['red_hits'] == red_hits_manual and 
            result['blue_hits'] == blue_hits_manual and 
            result['total_hits'] == total_hits_manual):
            print(f"  ✅ 比对引擎计算与手动计算一致")
            
            if total_hits_manual == 5:
                print(f"  ✅ 命中数正确: 5球")
                return True
            else:
                print(f"  ❌ 命中数错误: {total_hits_manual}球，期望5球")
                return False
        else:
            print(f"  ❌ 比对引擎计算与手动计算不一致")
            return False
    else:
        print(f"❌ 比对引擎没有返回结果")
        return False


def test_multiple_cases():
    """测试多个案例"""
    print(f"\n=== 测试多个案例 ===")
    
    test_cases = [
        {
            'name': '用户案例',
            'prediction': [2, 7, 10, 22, 27, 33, 14],
            'actual': [2, 7, 10, 27, 30, 33, 11],
            'expected_hits': 5
        },
        {
            'name': '全中案例',
            'prediction': [1, 2, 3, 4, 5, 6, 7],
            'actual': [1, 2, 3, 4, 5, 6, 7],
            'expected_hits': 7
        },
        {
            'name': '全不中案例',
            'prediction': [1, 2, 3, 4, 5, 6, 7],
            'actual': [8, 9, 10, 11, 12, 13, 14],
            'expected_hits': 0
        },
        {
            'name': '只中蓝球案例',
            'prediction': [1, 2, 3, 4, 5, 6, 7],
            'actual': [8, 9, 10, 11, 12, 13, 7],
            'expected_hits': 1
        }
    ]
    
    config = {}
    engine = ComparisonEngine(config)
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        prediction = {
            'red_balls': case['prediction'][:6],
            'blue_ball': case['prediction'][6],
            'method': case['name']
        }
        
        answer_data = [{
            'period': '25073',
            'red_balls': case['actual'][:6],
            'blue_ball': case['actual'][6]
        }]
        
        # 手动计算
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer_data[0]['red_balls'])
        red_hits_manual = len(pred_red_set & answer_red_set)
        blue_hits_manual = 1 if prediction['blue_ball'] == answer_data[0]['blue_ball'] else 0
        total_hits_manual = red_hits_manual + blue_hits_manual
        
        # 比对引擎计算
        results = engine._compare_single_prediction(prediction, answer_data)
        
        if results:
            result = results[0]
            
            print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
            print(f"  实际: {answer_data[0]['red_balls']} + {answer_data[0]['blue_ball']}")
            print(f"  手动计算: {total_hits_manual}球")
            print(f"  引擎计算: {result['total_hits']}球")
            print(f"  期望结果: {case['expected_hits']}球")
            
            if (result['total_hits'] == total_hits_manual == case['expected_hits']):
                print(f"  ✅ 通过")
            else:
                print(f"  ❌ 失败")
                all_passed = False
        else:
            print(f"  ❌ 比对引擎无结果")
            all_passed = False
    
    return all_passed


def main():
    """主函数"""
    print("手动比对验证工具")
    print("=" * 60)
    
    success1 = manual_comparison_tool()
    success2 = test_multiple_cases()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！")
        print("比对引擎逻辑完全正确")
        print("\n结论:")
        print("- 用户案例的正确答案是5球命中，不是6球")
        print("- 如果Excel显示6球，说明是旧版本文件或数据错误")
        print("- 建议重新运行分析生成最新的Excel文件")
    else:
        print("❌ 测试失败，需要进一步调查")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
