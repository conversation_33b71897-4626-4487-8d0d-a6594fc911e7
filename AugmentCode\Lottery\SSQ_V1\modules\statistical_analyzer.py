# -*- coding: utf-8 -*-
"""
统计分析模块 (Statistical Analyzer Module)

负责计算历史出现概率、大球数统计、冷球分析、重号分析等核心统计功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import Counter


class StatisticalAnalyzer:
    """
    统计分析器类
    
    负责各种统计分析计算
    """
    
    def __init__(self, config: Dict):
        """
        初始化统计分析器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.database = None
        
        # 概率表格存储
        self.red_ball_probabilities = None
        self.blue_ball_probabilities = None
        self.red_big_ball_probabilities = None
        self.blue_big_ball_probabilities = None
        self.red_cold_ball_probabilities = None
        self.blue_cold_ball_probabilities = None
        self.red_repeat_probabilities = None
        
        # 跟随性概率矩阵
        self.red_follow_matrix = None
        self.blue_follow_matrix = None
    
    def analyze(self, database: pd.DataFrame):
        """
        执行完整的统计分析
        
        Args:
            database: 数据库DataFrame
        """
        self.database = database
        
        # 计算历史出现概率
        self._calculate_historical_probabilities()
        
        # 计算大球数概率
        self._calculate_big_ball_probabilities()
        
        # 计算冷球数概率
        self._calculate_cold_ball_probabilities()
        
        # 计算重号数概率
        self._calculate_repeat_probabilities()
        
        # 计算跟随性概率矩阵
        self._calculate_follow_probabilities()
    
    def _calculate_historical_probabilities(self):
        """计算历史出现概率"""
        # 红球历史出现概率
        red_counts = Counter()
        for i in range(1, 7):
            red_counts.update(self.database[f'r{i}'].tolist())
        
        total_red = sum(red_counts.values())
        self.red_ball_probabilities = {}
        for ball in range(1, 34):
            count = red_counts.get(ball, 0)
            self.red_ball_probabilities[ball] = count / total_red if total_red > 0 else 0
        
        # 蓝球历史出现概率
        blue_counts = Counter(self.database['b'].tolist())
        total_blue = sum(blue_counts.values())
        self.blue_ball_probabilities = {}
        for ball in range(1, 17):
            count = blue_counts.get(ball, 0)
            self.blue_ball_probabilities[ball] = count / total_blue if total_blue > 0 else 0
    
    def _calculate_big_ball_probabilities(self):
        """计算大球数概率"""
        red_big_counts = Counter()
        blue_big_counts = Counter()
        
        for _, row in self.database.iterrows():
            # 红球大球数
            red_big_count = sum(1 for i in range(1, 7) 
                              if row[f'r{i}'] > self.config['red_big_ball_threshold'])
            red_big_counts[red_big_count] += 1
            
            # 蓝球大球数
            blue_big_count = 1 if row['b'] > self.config['blue_big_ball_threshold'] else 0
            blue_big_counts[blue_big_count] += 1
        
        # 红球大球数概率
        total_periods = len(self.database)
        self.red_big_ball_probabilities = {}
        for count in range(7):  # 0-6个大球
            self.red_big_ball_probabilities[count] = red_big_counts.get(count, 0) / total_periods
        
        # 蓝球大球数概率
        self.blue_big_ball_probabilities = {}
        for count in range(2):  # 0-1个大球
            self.blue_big_ball_probabilities[count] = blue_big_counts.get(count, 0) / total_periods
    
    def _calculate_cold_ball_probabilities(self):
        """计算冷球数概率"""
        red_cold_counts = Counter()
        blue_cold_counts = Counter()
        
        periods = self.config['cold_ball_periods']
        
        for i in range(periods, len(self.database)):
            current_row = self.database.iloc[i]
            
            # 获取前5期的红球和蓝球
            prev_red_balls = set()
            prev_blue_balls = set()
            
            for j in range(i - periods, i):
                prev_row = self.database.iloc[j]
                for k in range(1, 7):
                    prev_red_balls.add(prev_row[f'r{k}'])
                prev_blue_balls.add(prev_row['b'])
            
            # 计算当前期的冷球数
            current_red_balls = [current_row[f'r{k}'] for k in range(1, 7)]
            red_cold_count = sum(1 for ball in current_red_balls if ball not in prev_red_balls)
            red_cold_counts[red_cold_count] += 1
            
            current_blue_ball = current_row['b']
            blue_cold_count = 1 if current_blue_ball not in prev_blue_balls else 0
            blue_cold_counts[blue_cold_count] += 1
        
        # 红球冷球数概率
        total_valid_periods = len(self.database) - periods
        self.red_cold_ball_probabilities = {}
        for count in range(7):  # 0-6个冷球
            self.red_cold_ball_probabilities[count] = red_cold_counts.get(count, 0) / total_valid_periods if total_valid_periods > 0 else 0
        
        # 蓝球冷球数概率
        self.blue_cold_ball_probabilities = {}
        for count in range(2):  # 0-1个冷球
            self.blue_cold_ball_probabilities[count] = blue_cold_counts.get(count, 0) / total_valid_periods if total_valid_periods > 0 else 0
    
    def _calculate_repeat_probabilities(self):
        """计算重号数概率"""
        repeat_counts = Counter()
        
        for i in range(1, len(self.database)):
            current_row = self.database.iloc[i]
            prev_row = self.database.iloc[i - 1]
            
            current_red = set(current_row[f'r{k}'] for k in range(1, 7))
            prev_red = set(prev_row[f'r{k}'] for k in range(1, 7))
            
            repeat_count = len(current_red & prev_red)
            repeat_counts[repeat_count] += 1
        
        # 重号数概率
        total_valid_periods = len(self.database) - 1
        self.red_repeat_probabilities = {}
        for count in range(7):  # 0-6个重号
            self.red_repeat_probabilities[count] = repeat_counts.get(count, 0) / total_valid_periods if total_valid_periods > 0 else 0
    
    def _calculate_follow_probabilities(self):
        """计算跟随性概率矩阵"""
        # 初始化矩阵
        self.red_follow_matrix = np.zeros((33, 33))
        self.blue_follow_matrix = np.zeros((16, 16))
        
        # 红球跟随性统计
        red_follow_counts = np.zeros((33, 33))
        
        for i in range(1, len(self.database)):
            current_row = self.database.iloc[i]
            prev_row = self.database.iloc[i - 1]
            
            # 统计红球跟随关系
            for j in range(1, 7):
                prev_ball = prev_row[f'r{j}'] - 1  # 转换为0-32索引
                for k in range(1, 7):
                    current_ball = current_row[f'r{k}'] - 1  # 转换为0-32索引
                    red_follow_counts[current_ball, prev_ball] += 1
        
        # 转换为概率
        for col in range(33):
            col_sum = np.sum(red_follow_counts[:, col])
            if col_sum > 0:
                self.red_follow_matrix[:, col] = red_follow_counts[:, col] / col_sum
        
        # 蓝球跟随性统计
        blue_follow_counts = np.zeros((16, 16))
        
        for i in range(1, len(self.database)):
            current_row = self.database.iloc[i]
            prev_row = self.database.iloc[i - 1]
            
            prev_blue = prev_row['b'] - 1  # 转换为0-15索引
            current_blue = current_row['b'] - 1  # 转换为0-15索引
            blue_follow_counts[current_blue, prev_blue] += 1
        
        # 转换为概率
        for col in range(16):
            col_sum = np.sum(blue_follow_counts[:, col])
            if col_sum > 0:
                self.blue_follow_matrix[:, col] = blue_follow_counts[:, col] / col_sum
    
    def get_cold_balls(self, latest_period: Dict) -> Tuple[List[int], List[int]]:
        """
        获取冷球号码（在最近5期中未出现的号码）

        Args:
            latest_period: 最新一期数据

        Returns:
            (红球冷球号码列表, 蓝球冷球号码列表)
        """
        if self.database is None or len(self.database) < self.config['cold_ball_periods']:
            return [], []

        periods = self.config['cold_ball_periods']

        # 获取最近5期的红球和蓝球（包括最新一期）
        recent_red_balls = set()
        recent_blue_balls = set()

        # 从数据库末尾开始取最近5期
        start_index = len(self.database) - periods

        for j in range(start_index, len(self.database)):
            if j >= 0:
                row = self.database.iloc[j]
                for k in range(1, 7):
                    recent_red_balls.add(int(row[f'r{k}']))
                recent_blue_balls.add(int(row['b']))

        # 计算冷球：在号码范围内但未在最近5期出现的号码
        all_red_balls = set(range(1, 34))  # 1-33
        all_blue_balls = set(range(1, 17))  # 1-16

        red_cold_balls = sorted(list(all_red_balls - recent_red_balls))
        blue_cold_balls = sorted(list(all_blue_balls - recent_blue_balls))

        return red_cold_balls, blue_cold_balls

    def get_cold_balls_for_analysis(self, target_period: Dict, database) -> Tuple[List[int], List[int]]:
        """
        获取分析比对模式中的冷球号码（基于目标期号之前的5期数据）

        Args:
            target_period: 目标期号数据
            database: 当前数据库

        Returns:
            (红球冷球号码列表, 蓝球冷球号码列表)
        """
        if database is None or len(database) < self.config['cold_ball_periods']:
            return [], []

        periods = self.config['cold_ball_periods']
        target_period_num = int(target_period['period'])

        # 找到目标期号在数据库中的位置
        target_index = None
        database_reset = database.reset_index(drop=True)  # 重置索引确保连续

        for i in range(len(database_reset)):
            if database_reset.iloc[i]['NO'] == target_period_num:
                target_index = i
                break

        if target_index is None or target_index < periods:
            return [], []

        # 获取目标期号之前的5期数据
        recent_red_balls = set()
        recent_blue_balls = set()

        # 从目标期号之前的5期开始计算
        start_index = target_index - periods
        end_index = target_index  # 不包含目标期号本身

        for j in range(start_index, end_index):
            if j >= 0 and j < len(database_reset):
                row = database_reset.iloc[j]
                for k in range(1, 7):
                    recent_red_balls.add(int(row[f'r{k}']))
                recent_blue_balls.add(int(row['b']))

        # 计算冷球：在号码范围内但未在之前5期出现的号码
        all_red_balls = set(range(1, 34))  # 1-33
        all_blue_balls = set(range(1, 17))  # 1-16

        red_cold_balls = sorted(list(all_red_balls - recent_red_balls))
        blue_cold_balls = sorted(list(all_blue_balls - recent_blue_balls))

        return red_cold_balls, blue_cold_balls

    def get_most_probable_values(self) -> Dict:
        """
        获取概率最大的各项数值
        
        Returns:
            概率最大值字典
        """
        result = {}
        
        # 红球大球数概率最大值
        if self.red_big_ball_probabilities:
            result['red_big_ball_count'] = max(self.red_big_ball_probabilities.items(), key=lambda x: x[1])[0]
        
        # 蓝球大球数概率最大值
        if self.blue_big_ball_probabilities:
            result['blue_big_ball_count'] = max(self.blue_big_ball_probabilities.items(), key=lambda x: x[1])[0]
        
        # 红球冷球数概率最大值
        if self.red_cold_ball_probabilities:
            result['red_cold_ball_count'] = max(self.red_cold_ball_probabilities.items(), key=lambda x: x[1])[0]
        
        # 蓝球冷球数概率最大值
        if self.blue_cold_ball_probabilities:
            result['blue_cold_ball_count'] = max(self.blue_cold_ball_probabilities.items(), key=lambda x: x[1])[0]
        
        # 红球重号数概率最大值
        if self.red_repeat_probabilities:
            result['red_repeat_count'] = max(self.red_repeat_probabilities.items(), key=lambda x: x[1])[0]
        
        return result
    
    def get_probability_tables(self) -> Dict:
        """
        获取所有概率表格
        
        Returns:
            概率表格字典
        """
        return {
            'red_ball_probabilities': self.red_ball_probabilities,
            'blue_ball_probabilities': self.blue_ball_probabilities,
            'red_big_ball_probabilities': self.red_big_ball_probabilities,
            'blue_big_ball_probabilities': self.blue_big_ball_probabilities,
            'red_cold_ball_probabilities': self.red_cold_ball_probabilities,
            'blue_cold_ball_probabilities': self.blue_cold_ball_probabilities,
            'red_repeat_probabilities': self.red_repeat_probabilities,
            'red_follow_matrix': self.red_follow_matrix,
            'blue_follow_matrix': self.blue_follow_matrix
        }
