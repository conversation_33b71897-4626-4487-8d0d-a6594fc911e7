#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比对引擎的逻辑正确性
验证最大命中期号的选择逻辑
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_max_hit_logic():
    """测试最大命中期号的选择逻辑"""
    print("=== 测试最大命中期号选择逻辑 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定期号进行测试
    target_period = '25029'
    database_range = 200
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("❌ 无法获取答案数据")
        return False
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 进行比对
    comparison_results = system.comparison_engine.compare_predictions(predictions, answer_data)
    
    print(f"答案数据:")
    for answer in answer_data:
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  期号{answer['period']}: {red_str} + {answer['blue_ball']}")
    
    # 详细验证第1组的比对逻辑
    print(f"\n=== 详细验证第1组比对逻辑 ===")
    
    group_1_result = comparison_results[1]
    prediction_1 = group_1_result['prediction']
    results_1 = group_1_result['results']
    max_hit_1 = group_1_result['max_hit']
    
    pred_red = set(prediction_1['red_balls'])
    pred_blue = prediction_1['blue_ball']
    pred_red_str = ' '.join(map(str, sorted(pred_red)))
    
    print(f"第1组预测: {pred_red_str} + {pred_blue}")
    
    # 手动计算每期的命中情况
    manual_results = []
    for answer in answer_data:
        answer_red = set(answer['red_balls'])
        answer_blue = answer['blue_ball']
        
        red_hits = len(pred_red & answer_red)
        blue_hits = 1 if pred_blue == answer_blue else 0
        total_hits = red_hits + blue_hits
        
        manual_results.append({
            'period': answer['period'],
            'total_hits': total_hits,
            'red_hits': red_hits,
            'blue_hits': blue_hits
        })
        
        answer_red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  期号{answer['period']}: {answer_red_str} + {answer_blue} -> 红{red_hits}+蓝{blue_hits}={total_hits}")
    
    # 找到最大命中（按总命中数，然后按期号）
    max_total = max(manual_results, key=lambda x: x['total_hits'])['total_hits']
    max_candidates = [r for r in manual_results if r['total_hits'] == max_total]
    manual_max_hit = max(max_candidates, key=lambda x: int(x['period']))
    
    print(f"\n手动计算的最大命中:")
    print(f"  最大命中球数: {manual_max_hit['total_hits']}")
    print(f"  最大命中期号: {manual_max_hit['period']}")
    print(f"  红球命中数: {manual_max_hit['red_hits']}")
    print(f"  蓝球命中数: {manual_max_hit['blue_hits']}")
    
    print(f"\n比对引擎的最大命中:")
    print(f"  最大命中球数: {max_hit_1['total_hits']}")
    print(f"  最大命中期号: {max_hit_1['period']}")
    print(f"  红球命中数: {max_hit_1['red_hits']}")
    print(f"  蓝球命中数: {max_hit_1['blue_hits']}")
    
    # 验证是否一致
    if (manual_max_hit['total_hits'] == max_hit_1['total_hits'] and
        manual_max_hit['period'] == max_hit_1['period'] and
        manual_max_hit['red_hits'] == max_hit_1['red_hits'] and
        manual_max_hit['blue_hits'] == max_hit_1['blue_hits']):
        print(f"\n✅ 比对引擎逻辑正确！")
        return True
    else:
        print(f"\n❌ 比对引擎逻辑有误！")
        return False


def test_specific_case():
    """测试特定的比对案例"""
    print(f"\n=== 测试特定比对案例 ===")
    
    # 模拟一个预测
    prediction = {
        'method': '测试方法',
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 1
    }
    
    # 模拟答案数据（故意设计多个期号有相同的最大命中数）
    answer_data = [
        {'period': '25001', 'red_balls': [1, 7, 8, 9, 10, 11], 'blue_ball': 2},  # 红1+蓝0=1
        {'period': '25002', 'red_balls': [2, 7, 8, 9, 10, 11], 'blue_ball': 3},  # 红1+蓝0=1
        {'period': '25003', 'red_balls': [3, 7, 8, 9, 10, 11], 'blue_ball': 1},  # 红1+蓝1=2 (最大)
        {'period': '25004', 'red_balls': [4, 7, 8, 9, 10, 11], 'blue_ball': 4},  # 红1+蓝0=1
        {'period': '25005', 'red_balls': [5, 6, 8, 9, 10, 11], 'blue_ball': 5},  # 红2+蓝0=2 (最大)
    ]
    
    print(f"测试预测: {' '.join(map(str, prediction['red_balls']))} + {prediction['blue_ball']}")
    
    # 初始化比对引擎
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 手动计算比对结果
    pred_red = set(prediction['red_balls'])
    pred_blue = prediction['blue_ball']
    
    results = []
    for answer in answer_data:
        answer_red = set(answer['red_balls'])
        answer_blue = answer['blue_ball']
        
        red_hits = len(pred_red & answer_red)
        blue_hits = 1 if pred_blue == answer_blue else 0
        total_hits = red_hits + blue_hits
        
        results.append({
            'period': answer['period'],
            'total_hits': total_hits,
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'answer_red_balls': answer['red_balls'],
            'answer_blue_ball': answer['blue_ball']
        })
        
        answer_red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  期号{answer['period']}: {answer_red_str} + {answer_blue} -> 红{red_hits}+蓝{blue_hits}={total_hits}")
    
    # 找到最大命中（按总命中数，然后按期号）
    max_total = max(results, key=lambda x: x['total_hits'])['total_hits']
    max_candidates = [r for r in results if r['total_hits'] == max_total]
    manual_max_hit = max(max_candidates, key=lambda x: int(x['period']))
    
    print(f"\n预期的最大命中:")
    print(f"  最大命中球数: {manual_max_hit['total_hits']}")
    print(f"  最大命中期号: {manual_max_hit['period']}")
    print(f"  红球命中数: {manual_max_hit['red_hits']}")
    print(f"  蓝球命中数: {manual_max_hit['blue_hits']}")
    
    # 使用比对引擎计算
    engine_results = system.comparison_engine._compare_single_prediction(prediction, answer_data)
    engine_max_hit = max(engine_results, key=lambda x: (x['total_hits'], int(x['period'])))
    
    print(f"\n比对引擎的最大命中:")
    print(f"  最大命中球数: {engine_max_hit['total_hits']}")
    print(f"  最大命中期号: {engine_max_hit['period']}")
    print(f"  红球命中数: {engine_max_hit['red_hits']}")
    print(f"  蓝球命中数: {engine_max_hit['blue_hits']}")
    
    # 验证是否一致
    if (manual_max_hit['total_hits'] == engine_max_hit['total_hits'] and
        manual_max_hit['period'] == engine_max_hit['period'] and
        manual_max_hit['red_hits'] == engine_max_hit['red_hits'] and
        manual_max_hit['blue_hits'] == engine_max_hit['blue_hits']):
        print(f"\n✅ 特定案例测试通过！")
        return True
    else:
        print(f"\n❌ 特定案例测试失败！")
        return False


def main():
    """主测试函数"""
    print("比对引擎逻辑正确性测试")
    print("=" * 60)
    
    success1 = test_max_hit_logic()
    success2 = test_specific_case()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 比对引擎逻辑正确性测试通过！")
        return True
    else:
        print("⚠️ 比对引擎逻辑需要修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
