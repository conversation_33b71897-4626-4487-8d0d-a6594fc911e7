#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试分析比对中的重号数问题
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_analysis_repeat_count():
    """调试分析比对中的重号数计算"""
    print("=== 调试分析比对中的重号数计算 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定的期号进行测试
    target_period = '25050'
    database_range = 200
    answer_periods = 6
    
    print(f"目标期号: {target_period}")
    print(f"数据范围: {database_range} 期")
    
    # 获取分析数据
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    print(f"分析期号列表: {analysis_periods}")
    
    # 模拟分析比对循环，找到25050期
    for i, period in enumerate(analysis_periods):
        if period == '25050':  # 找到25050期
            print(f"\n=== 第{i+1}期分析 (期号: {period}) ===")
            
            # 获取当前期的数据
            current_database = system.data_loader.get_database_for_period(period, database_range)
            latest_period = system.data_loader.get_latest_period(current_database)
            
            print(f"当前数据库最新期: {latest_period['period']}")
            red_str = ' '.join(map(str, latest_period['red_balls']))
            print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
            
            # 检查是否是期号25050
            if latest_period['period'] == '25050':
                print("✓ 确认是期号25050")
                
                # 手动查找上一期
                current_period_num = int(latest_period['period'])
                prev_period_num = current_period_num - 1
                
                print(f"查找上一期: {prev_period_num}")
                
                prev_row = None
                for _, row in current_database.iterrows():
                    if row['NO'] == prev_period_num:
                        prev_row = row
                        break
                
                if prev_row is not None:
                    prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
                    prev_blue_ball = int(prev_row['b'])
                    prev_red_str = ' '.join(map(str, prev_red_balls))
                    print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
                    
                    # 计算重号
                    current_red_balls = latest_period['red_balls']
                    repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
                    manual_repeat_count = len(repeat_balls)
                    
                    print(f"当前期红球: {current_red_balls}")
                    print(f"上一期红球: {prev_red_balls}")
                    print(f"重号球: {repeat_balls}")
                    print(f"手动计算重号数: {manual_repeat_count}")
                    
                    # 测试系统方法
                    system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
                    print(f"系统计算重号数: {system_repeat_count}")
                    
                    if manual_repeat_count == system_repeat_count == 1:
                        print("✅ 重号数计算正确")
                    else:
                        print("❌ 重号数计算错误")
                        return False
                else:
                    print("❌ 未找到上一期数据")
                    return False
            else:
                print(f"⚠️ 期号不匹配: 期望25050, 实际{latest_period['period']}")
            
            break
    
    return True


def debug_full_analysis_process():
    """调试完整的分析比对过程"""
    print(f"\n=== 调试完整的分析比对过程 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 运行分析比对，但只分析前50期
    target_period = '25050'
    database_range = 200
    answer_periods = 6
    
    print(f"开始分析比对...")
    
    # 获取分析期号
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    # 只分析前50期
    limited_periods = analysis_periods[:50]
    
    results = []
    
    for i, period in enumerate(limited_periods):
        # 获取当前期的数据
        current_database = system.data_loader.get_database_for_period(period, database_range)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 找到25050期时显示进度
        if period == '25050':
            print(f"\n=== 第{i+1}期进度显示 ===")
            print(f"分析期号: {period}")
            print(f"数据库最新期: {latest_period['period']}")
            
            # 获取冷球信息
            pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
            analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
            
            # 获取筛选要求
            filter_requirements = system.prediction_engine.filter_requirements
            
            # 计算重号数
            repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
            
            print(f"计算的重号数: {repeat_count}")
            
            # 如果是期号25050，进行详细验证
            if latest_period['period'] == '25050':
                print(f"✓ 找到期号25050，重号数: {repeat_count}")
                
                # 显示进度
                system.ui.show_analysis_progress(
                    i + 1, len(limited_periods),
                    len(current_database), latest_period,
                    predictions, filter_requirements,
                    (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
                    (analysis_red_cold_balls, analysis_blue_cold_balls)
                )
                
                return repeat_count == 1
    
    return False


def main():
    """主函数"""
    print("分析比对重号数调试")
    print("=" * 60)
    
    success1 = debug_analysis_repeat_count()
    success2 = debug_full_analysis_process()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 调试完成，重号数计算正确！")
        return True
    else:
        print("⚠️ 发现问题，需要进一步修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
