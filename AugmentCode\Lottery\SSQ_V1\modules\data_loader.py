# -*- coding: utf-8 -*-
"""
数据加载模块 (Data Loader Module)

负责从Excel文件中读取双色球历史数据，进行数据清洗和验证，
并根据用户需求提供不同范围的数据集。
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Tuple
import re


class DataLoader:
    """
    数据加载器类
    
    负责加载、清洗和管理双色球历史数据
    """
    
    def __init__(self, data_file_path: str):
        """
        初始化数据加载器
        
        Args:
            data_file_path: Excel数据文件路径
        """
        self.data_file_path = data_file_path
        self.original_database = None
        self._load_original_data()
    
    def _load_original_data(self):
        """加载原始数据"""
        try:
            # 读取Excel文件中的SSQ_data_all工作表
            df = pd.read_excel(
                self.data_file_path, 
                sheet_name='SSQ_data_all',
                usecols=['NO'] + [f'r{i}' for i in range(1, 7)] + ['b']  # A列和I-O列
            )
            
            # 数据清洗
            df = self._clean_data(df)
            
            # 按期号排序（从小到大）
            df = df.sort_values('NO').reset_index(drop=True)
            
            self.original_database = df
            print(f"成功加载 {len(df)} 期历史数据")
            
        except Exception as e:
            raise Exception(f"加载数据文件失败: {e}")
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        # 删除空行
        df = df.dropna()
        
        # 验证期号格式（5位数字）
        df = df[df['NO'].astype(str).str.match(r'^\d{5}$')]
        
        # 验证红球号码范围（1-33）
        red_cols = [f'r{i}' for i in range(1, 7)]
        for col in red_cols:
            df = df[(df[col] >= 1) & (df[col] <= 33)]
        
        # 验证蓝球号码范围（1-16）
        df = df[(df['b'] >= 1) & (df['b'] <= 16)]
        
        # 确保数据类型正确
        df['NO'] = df['NO'].astype(int)
        for col in red_cols + ['b']:
            df[col] = df[col].astype(int)
        
        return df
    
    def get_current_database(self, range_size: int = 0) -> Optional[pd.DataFrame]:
        """
        获取当前数据库
        
        Args:
            range_size: 数据范围大小，0表示使用全部数据
            
        Returns:
            当前数据库DataFrame
        """
        if self.original_database is None or len(self.original_database) == 0:
            return None
        
        if range_size == 0:
            return self.original_database.copy()
        
        # 获取最新的range_size期数据
        return self.original_database.tail(range_size).copy()
    
    def get_database_for_period(self, target_period: str, range_size: int = 0) -> Optional[pd.DataFrame]:
        """
        获取指定期号及之前的数据库
        
        Args:
            target_period: 目标期号
            range_size: 数据范围大小，0表示使用全部数据
            
        Returns:
            数据库DataFrame
        """
        if self.original_database is None:
            return None
        
        target_period_int = int(target_period)
        
        # 获取目标期号及之前的数据
        mask = self.original_database['NO'] <= target_period_int
        filtered_data = self.original_database[mask]
        
        if len(filtered_data) == 0:
            return None
        
        if range_size == 0:
            return filtered_data.copy()
        
        # 获取最新的range_size期数据
        return filtered_data.tail(range_size).copy()
    
    def get_latest_period(self, database: pd.DataFrame) -> Optional[Dict]:
        """
        获取数据库中最新一期的信息
        
        Args:
            database: 数据库DataFrame
            
        Returns:
            最新一期信息字典
        """
        if database is None or len(database) == 0:
            return None
        
        latest_row = database.iloc[-1]
        
        return {
            'period': str(latest_row['NO']),
            'red_balls': [int(latest_row[f'r{i}']) for i in range(1, 7)],
            'blue_ball': int(latest_row['b'])
        }
    
    def get_answer_data(self, target_period: str) -> Optional[List[Dict]]:
        """
        获取目标期号之后连续6期的答案数据
        
        Args:
            target_period: 目标期号
            
        Returns:
            答案数据列表
        """
        if self.original_database is None:
            return None
        
        target_period_int = int(target_period)
        
        # 获取目标期号之后的数据
        mask = self.original_database['NO'] > target_period_int
        future_data = self.original_database[mask]
        
        if len(future_data) < 6:
            return None
        
        # 获取连续6期数据
        answer_periods = future_data.head(6)
        
        answer_data = []
        for _, row in answer_periods.iterrows():
            answer_data.append({
                'period': str(row['NO']),
                'red_balls': [int(row[f'r{i}']) for i in range(1, 7)],
                'blue_ball': int(row['b'])
            })
        
        return answer_data
    
    def get_analysis_periods(self, start_period: str) -> List[str]:
        """
        获取可以进行分析的期号列表
        
        Args:
            start_period: 开始期号
            
        Returns:
            可分析期号列表
        """
        if self.original_database is None:
            return []
        
        start_period_int = int(start_period)
        
        # 找到开始期号在数据中的位置
        start_mask = self.original_database['NO'] >= start_period_int
        available_data = self.original_database[start_mask]
        
        if len(available_data) == 0:
            return []
        
        # 确保每个期号之后都有至少6期数据用于答案比对
        analysis_periods = []
        
        for i, row in available_data.iterrows():
            period = str(row['NO'])
            
            # 检查该期号之后是否有足够的数据
            future_mask = self.original_database['NO'] > row['NO']
            future_count = len(self.original_database[future_mask])
            
            if future_count >= 6:
                analysis_periods.append(period)
        
        return analysis_periods
    
    def get_next_period(self, current_period: str) -> Optional[str]:
        """
        获取下一期期号
        
        Args:
            current_period: 当前期号
            
        Returns:
            下一期期号，如果不存在则返回None
        """
        if self.original_database is None:
            return None
        
        current_period_int = int(current_period)
        
        # 查找下一期
        next_mask = self.original_database['NO'] > current_period_int
        next_data = self.original_database[next_mask]
        
        if len(next_data) == 0:
            return None
        
        return str(next_data.iloc[0]['NO'])
    
    def get_period_data(self, period: str) -> Optional[Dict]:
        """
        获取指定期号的数据
        
        Args:
            period: 期号
            
        Returns:
            期号数据字典
        """
        if self.original_database is None:
            return None
        
        period_int = int(period)
        mask = self.original_database['NO'] == period_int
        period_data = self.original_database[mask]
        
        if len(period_data) == 0:
            return None
        
        row = period_data.iloc[0]
        return {
            'period': str(row['NO']),
            'red_balls': [int(row[f'r{i}']) for i in range(1, 7)],
            'blue_ball': int(row['b'])
        }
    
    def get_database_info(self, database: pd.DataFrame) -> Dict:
        """
        获取数据库信息
        
        Args:
            database: 数据库DataFrame
            
        Returns:
            数据库信息字典
        """
        if database is None or len(database) == 0:
            return {
                'total_periods': 0,
                'start_period': None,
                'end_period': None,
                'years_covered': []
            }
        
        start_period = str(database.iloc[0]['NO'])
        end_period = str(database.iloc[-1]['NO'])
        
        # 提取年份信息
        years = set()
        for period in database['NO']:
            year_part = str(period)[:2]
            if year_part.isdigit():
                year = 2000 + int(year_part)
                years.add(year)
        
        return {
            'total_periods': len(database),
            'start_period': start_period,
            'end_period': end_period,
            'years_covered': sorted(list(years))
        }
