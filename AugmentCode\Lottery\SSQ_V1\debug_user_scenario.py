#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟用户的实际运行场景
调试第50期显示重号数为0的问题
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def simulate_user_analysis():
    """模拟用户的分析比对运行"""
    print("=== 模拟用户的分析比对运行 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 根据用户截图，目标期号是25050，显示"已完成：50/75 期"
    # 这意味着用户运行了75期的分析比对
    target_period = '25050'
    database_range = 99  # 用户截图显示"当前数据库包含：99 期数据"
    
    print(f"目标期号: {target_period}")
    print(f"数据库范围: {database_range} 期")
    
    # 获取分析期号列表
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    print(f"分析期号列表: {analysis_periods}")
    print(f"总分析期数: {len(analysis_periods)}")
    
    # 模拟分析比对过程，找到第50期
    for i, period in enumerate(analysis_periods):
        if (i + 1) == 50:  # 第50期
            print(f"\n=== 第{i+1}期分析 (期号: {period}) ===")
            
            # 获取当前期的数据
            current_database = system.data_loader.get_database_for_period(period, database_range)
            latest_period = system.data_loader.get_latest_period(current_database)
            
            print(f"分析期号: {period}")
            print(f"数据库最新期: {latest_period['period']}")
            red_str = ' '.join(map(str, latest_period['red_balls']))
            print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
            
            # 手动查找上一期
            current_period_num = int(latest_period['period'])
            prev_period_num = current_period_num - 1
            
            print(f"查找上一期: {prev_period_num}")
            
            prev_row = None
            for _, row in current_database.iterrows():
                if row['NO'] == prev_period_num:
                    prev_row = row
                    break
            
            if prev_row is not None:
                prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
                prev_blue_ball = int(prev_row['b'])
                prev_red_str = ' '.join(map(str, prev_red_balls))
                print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
                
                # 计算重号
                current_red_balls = latest_period['red_balls']
                repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
                manual_repeat_count = len(repeat_balls)
                
                print(f"当前期红球: {current_red_balls}")
                print(f"上一期红球: {prev_red_balls}")
                print(f"重号球: {repeat_balls}")
                print(f"手动计算重号数: {manual_repeat_count}")
                
                # 测试系统方法
                system_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
                print(f"系统计算重号数: {system_repeat_count}")
                
                # 运行完整的分析流程
                print(f"\n=== 运行完整分析流程 ===")
                
                # 运行分析
                system.statistical_analyzer.analyze(current_database)
                system.markov_analyzer.analyze(current_database, latest_period)
                system.bayesian_analyzer.analyze(current_database, latest_period)
                
                # 生成预测
                predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
                
                # 获取冷球信息
                pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
                analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
                
                # 获取筛选要求
                filter_requirements = system.prediction_engine.filter_requirements
                
                # 显示进度
                print(f"\n=== 第{i+1}期进度显示 ===")
                system.ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(current_database), latest_period,
                    predictions, filter_requirements,
                    (pred_red_cold_balls, pred_blue_cold_balls), system_repeat_count,
                    (analysis_red_cold_balls, analysis_blue_cold_balls)
                )
                
                return system_repeat_count
            else:
                print("❌ 未找到上一期数据")
                return None
            
            break
    
    return None


def find_period_25050_in_analysis():
    """查找25050期在分析比对中的位置"""
    print(f"\n=== 查找25050期在分析比对中的位置 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    target_period = '25050'
    analysis_periods = system.data_loader.get_analysis_periods(target_period)
    
    print(f"分析期号列表: {analysis_periods}")
    
    # 查找25050期在哪个位置
    for i, period in enumerate(analysis_periods):
        if period == '25050':
            print(f"✓ 25050期是第{i+1}期分析")
            return i + 1
        
        # 检查数据库最新期是否是25050
        current_database = system.data_loader.get_database_for_period(period, 99)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        if latest_period['period'] == '25050':
            print(f"✓ 数据库最新期25050出现在第{i+1}期分析 (分析期号: {period})")
            return i + 1
    
    print("❌ 未找到25050期")
    return None


def main():
    """主函数"""
    print("用户场景调试")
    print("=" * 60)
    
    # 查找25050期的位置
    position = find_period_25050_in_analysis()
    
    # 模拟用户的第50期分析
    repeat_count = simulate_user_analysis()
    
    print("\n" + "=" * 60)
    if position is not None:
        print(f"✓ 25050期在第{position}期分析中")
        if position == 50:
            print("✓ 25050期正好是第50期分析")
            if repeat_count == 1:
                print("🎉 第50期重号数计算正确！")
                return True
            else:
                print(f"❌ 第50期重号数错误: {repeat_count}")
                return False
        else:
            print(f"⚠️ 25050期不是第50期分析，而是第{position}期")
            print("用户看到的第50期重号数为0可能是正确的（不同期号）")
            return True
    else:
        print("❌ 未找到25050期")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
