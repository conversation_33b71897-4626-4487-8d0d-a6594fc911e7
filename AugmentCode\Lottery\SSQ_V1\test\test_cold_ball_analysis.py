#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析比对模式中的冷球计算修正
验证最新1期号码的冷球数计算是否正确
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_cold_ball_calculation_difference():
    """测试预测模式和分析模式的冷球计算差异"""
    print("=== 测试冷球计算差异 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定的历史数据进行测试
    target_period = '25070'
    database_range = 100
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"测试期号: {target_period}")
    print(f"数据库期数: {len(current_database)}")
    print(f"最新期号: {latest_period['period']}")
    
    # 显示最新期号码
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 运行统计分析
    system.statistical_analyzer.analyze(current_database)
    
    # 1. 预测模式的冷球计算（包含最新1期的最近5期）
    pred_red_cold, pred_blue_cold = system.statistical_analyzer.get_cold_balls(latest_period)
    
    # 2. 分析模式的冷球计算（最新1期之前的5期）
    analysis_red_cold, analysis_blue_cold = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    print(f"\n=== 预测模式冷球计算（包含最新1期的最近5期）===")
    print(f"红球冷球: {pred_red_cold}")
    print(f"蓝球冷球: {pred_blue_cold}")
    
    print(f"\n=== 分析模式冷球计算（最新1期之前的5期）===")
    print(f"红球冷球: {analysis_red_cold}")
    print(f"蓝球冷球: {analysis_blue_cold}")
    
    # 计算最新1期号码的冷球数
    print(f"\n=== 最新1期号码的冷球数计算 ===")
    
    # 使用预测模式冷球计算
    pred_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in pred_red_cold)
    pred_blue_cold_count = 1 if latest_period['blue_ball'] in pred_blue_cold else 0
    
    # 使用分析模式冷球计算
    analysis_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in analysis_red_cold)
    analysis_blue_cold_count = 1 if latest_period['blue_ball'] in analysis_blue_cold else 0
    
    print(f"基于预测模式冷球的最新1期冷球数: 红{pred_red_cold_count} 蓝{pred_blue_cold_count}")
    print(f"基于分析模式冷球的最新1期冷球数: 红{analysis_red_cold_count} 蓝{analysis_blue_cold_count}")
    
    # 手动验证分析模式的计算
    print(f"\n=== 手动验证分析模式计算 ===")
    
    # 找到最新期在数据库中的位置
    latest_period_num = int(latest_period['period'])
    latest_index = None
    for i, row in current_database.iterrows():
        if row['NO'] == latest_period_num:
            latest_index = i
            break
    
    if latest_index is not None and latest_index >= 5:
        print(f"最新期在数据库中的索引: {latest_index}")
        
        # 获取之前5期的数据
        prev_5_periods = current_database.iloc[latest_index-5:latest_index]
        print(f"之前5期数据:")
        
        prev_red_balls = set()
        prev_blue_balls = set()
        
        for i, (_, row) in enumerate(prev_5_periods.iterrows()):
            red_balls = [int(row[f'r{j}']) for j in range(1, 7)]
            blue_ball = int(row['b'])
            red_str = ' '.join(map(str, red_balls))
            print(f"  期号{row['NO']}: {red_str} + {blue_ball}")
            
            for ball in red_balls:
                prev_red_balls.add(ball)
            prev_blue_balls.add(blue_ball)
        
        print(f"之前5期出现的红球: {sorted(prev_red_balls)}")
        print(f"之前5期出现的蓝球: {sorted(prev_blue_balls)}")
        
        # 计算手动冷球
        all_red = set(range(1, 34))
        all_blue = set(range(1, 17))
        manual_red_cold = sorted(all_red - prev_red_balls)
        manual_blue_cold = sorted(all_blue - prev_blue_balls)
        
        print(f"手动计算的红球冷球: {manual_red_cold}")
        print(f"手动计算的蓝球冷球: {manual_blue_cold}")
        
        # 验证是否一致
        if manual_red_cold == analysis_red_cold and manual_blue_cold == analysis_blue_cold:
            print("✅ 分析模式冷球计算正确！")
        else:
            print("❌ 分析模式冷球计算有误！")
        
        # 计算手动的最新1期冷球数
        manual_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in manual_red_cold)
        manual_blue_cold_count = 1 if latest_period['blue_ball'] in manual_blue_cold else 0
        
        print(f"手动计算的最新1期冷球数: 红{manual_red_cold_count} 蓝{manual_blue_cold_count}")
        
        return analysis_red_cold_count == manual_red_cold_count and analysis_blue_cold_count == manual_blue_cold_count
    
    return False


def test_analysis_progress_display():
    """测试分析进度显示中的冷球数"""
    print(f"\n=== 测试分析进度显示中的冷球数 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定的历史数据进行测试
    target_period = '25070'
    database_range = 100
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取两种冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    # 获取筛选要求
    filter_requirements = system.prediction_engine.filter_requirements
    
    # 计算重号数
    repeat_count = system._calculate_repeat_count(current_database, latest_period)
    
    # 显示进度信息
    print(f"模拟分析进度显示:")
    system.ui.show_analysis_progress(
        50, 100,  # 假设完成50/100期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    return True


def main():
    """主测试函数"""
    print("分析比对模式冷球计算修正测试")
    print("=" * 60)
    
    success1 = test_cold_ball_calculation_difference()
    success2 = test_analysis_progress_display()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 分析比对模式冷球计算修正测试通过！")
        return True
    else:
        print("⚠️ 冷球计算仍需修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
