#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索用户提到的预测号码
在所有期号中查找匹配的预测
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def search_user_prediction():
    """搜索用户提到的预测号码"""
    print("=== 搜索用户提到的预测号码 ===")
    
    # 用户提到的号码
    user_red_balls = [2, 7, 10, 22, 27, 33]
    user_blue_ball = 14
    
    print(f"搜索号码: {user_red_balls} + {user_blue_ball}")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    try:
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        print(f"搜索范围: {len(analysis_periods)} 期")
        
        found_matches = []
        
        # 搜索前20期
        for i, period in enumerate(analysis_periods[:20]):
            print(f"搜索期号 {period} ({i+1}/20)")
            
            try:
                # 获取当前数据库
                current_database = system.data_loader.get_database_for_period(
                    period, database_range
                )
                
                if current_database is None or len(current_database) == 0:
                    continue
                
                # 运行预测
                latest_period = system.data_loader.get_latest_period(current_database)
                
                # 分析
                system.statistical_analyzer.analyze(current_database)
                system.markov_analyzer.analyze(current_database, latest_period)
                system.bayesian_analyzer.analyze(current_database, latest_period)
                
                # 生成预测
                predictions = system.prediction_engine.generate_all_predictions(
                    current_database, latest_period
                )
                
                # 检查是否有匹配的预测
                for group_id in range(1, 25):
                    if group_id in predictions:
                        prediction = predictions[group_id]
                        red_balls = prediction['red_balls']
                        blue_ball = prediction['blue_ball']
                        
                        # 转换numpy类型为普通int
                        red_balls_clean = [int(x) for x in red_balls]
                        blue_ball_clean = int(blue_ball)
                        
                        # 检查是否匹配
                        if (red_balls_clean == user_red_balls and blue_ball_clean == user_blue_ball):
                            found_matches.append({
                                'period': period,
                                'group_id': group_id,
                                'method': prediction['method'],
                                'red_balls': red_balls_clean,
                                'blue_ball': blue_ball_clean
                            })
                            print(f"  ✅ 找到匹配: 第{group_id}组 ({prediction['method']})")
            
            except Exception as e:
                print(f"  期号 {period} 处理出错: {e}")
                continue
        
        print(f"\n=== 搜索结果 ===")
        if found_matches:
            print(f"找到 {len(found_matches)} 个匹配的预测:")
            
            for match in found_matches:
                print(f"  期号: {match['period']}")
                print(f"  组号: 第{match['group_id']}组")
                print(f"  方法: {match['method']}")
                print(f"  号码: {match['red_balls']} + {match['blue_ball']}")
                print()
                
                # 如果找到匹配，验证比对结果
                if match['period'] == '25067':
                    return verify_match_comparison(system, match, database_range)
            
            # 如果没有25067期的匹配，检查第一个匹配
            if found_matches:
                first_match = found_matches[0]
                print(f"验证第一个匹配的比对结果:")
                return verify_match_comparison(system, first_match, database_range)
        else:
            print("❌ 未找到匹配的预测号码")
            print("可能的原因：")
            print("1. 用户查看的是更早期号的预测")
            print("2. 用户查看的是手动修改过的预测")
            print("3. 预测算法已经更新")
            print("4. 用户记错了号码")
            return False
        
    except Exception as e:
        print(f"搜索过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_match_comparison(system, match, database_range):
    """验证匹配预测的比对结果"""
    print(f"=== 验证 {match['period']} 期第{match['group_id']}组的比对结果 ===")
    
    try:
        period = match['period']
        
        # 获取数据库和答案
        current_database = system.data_loader.get_database_for_period(period, database_range)
        answer_data = system.data_loader.get_answer_data(period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        if match['group_id'] in comparison_result:
            group_result = comparison_result[match['group_id']]
            max_hit = group_result['max_hit']
            
            print(f"预测号码: {match['red_balls']} + {match['blue_ball']}")
            print(f"预测方法: {match['method']}")
            print(f"最大命中: {max_hit['total_hits']}球")
            print(f"最大命中期号: {max_hit['period']}")
            print(f"红球命中数: {max_hit['red_hits']}")
            print(f"蓝球命中数: {max_hit['blue_hits']}")
            
            if max_hit['period'] == '25073':
                print(f"实际号码: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
                
                # 手动验证
                pred_red_set = set(match['red_balls'])
                answer_red_set = set(max_hit['answer_red_balls'])
                red_hits_manual = len(pred_red_set & answer_red_set)
                blue_hits_manual = 1 if match['blue_ball'] == max_hit['answer_blue_ball'] else 0
                total_hits_manual = red_hits_manual + blue_hits_manual
                
                print(f"\n手动验证:")
                print(f"  预测红球集合: {pred_red_set}")
                print(f"  实际红球集合: {answer_red_set}")
                print(f"  红球交集: {pred_red_set & answer_red_set}")
                print(f"  手动计算总命中数: {total_hits_manual}")
                
                if max_hit['total_hits'] == total_hits_manual == 5:
                    print(f"  ✅ 比对结果正确: 5球命中")
                    return True
                elif max_hit['total_hits'] == 6 and total_hits_manual == 5:
                    print(f"  ❌ 比对结果错误: 系统显示6球，应该是5球")
                    return False
                else:
                    print(f"  ⚠️ 意外结果: 系统{max_hit['total_hits']}球，手动{total_hits_manual}球")
                    return False
        
        return False
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        return False


def main():
    """主函数"""
    print("搜索用户提到的预测号码")
    print("=" * 60)
    
    success = search_user_prediction()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 找到匹配的预测并验证正确！")
    else:
        print("❌ 未找到匹配的预测或验证失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
