#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高命中数详细信息打印功能
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_high_hit_details_function():
    """测试高命中数详细信息打印功能"""
    print("=== 测试高命中数详细信息打印功能 ===")
    
    # 创建系统实例
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 模拟分析结果数据，包含6球和7球命中的案例
    mock_results = [
        {
            'period': '25001',
            'predictions': {
                1: {
                    'red_balls': [1, 2, 3, 4, 5, 6],
                    'blue_ball': 7,
                    'method': '历史出现概率'
                },
                2: {
                    'red_balls': [7, 8, 9, 10, 11, 12],
                    'blue_ball': 13,
                    'method': '马尔科夫链'
                }
            },
            'comparison': {
                1: {
                    'max_hit': {
                        'period': '25005',
                        'total_hits': 6,
                        'red_hits': 6,
                        'blue_hits': 0,
                        'answer_red_balls': [1, 2, 3, 4, 5, 6],
                        'answer_blue_ball': 14,
                        'blue_hit_status': False
                    }
                },
                2: {
                    'max_hit': {
                        'period': '25006',
                        'total_hits': 7,
                        'red_hits': 6,
                        'blue_hits': 1,
                        'answer_red_balls': [7, 8, 9, 10, 11, 12],
                        'answer_blue_ball': 13,
                        'blue_hit_status': True
                    }
                }
            },
            'database_size': 100,
            'latest_period': {'period': '25001', 'red_balls': [1, 5, 10, 17, 20, 22], 'blue_ball': 5}
        },
        {
            'period': '25002',
            'predictions': {
                3: {
                    'red_balls': [15, 16, 17, 18, 19, 20],
                    'blue_ball': 21,
                    'method': '贝叶斯概率'
                }
            },
            'comparison': {
                3: {
                    'max_hit': {
                        'period': '25007',
                        'total_hits': 5,
                        'red_hits': 4,
                        'blue_hits': 1,
                        'answer_red_balls': [15, 16, 17, 18, 25, 26],
                        'answer_blue_ball': 21,
                        'blue_hit_status': True
                    }
                }
            },
            'database_size': 100,
            'latest_period': {'period': '25002', 'red_balls': [2, 6, 11, 18, 21, 23], 'blue_ball': 8}
        },
        {
            'period': '25003',
            'predictions': {
                4: {
                    'red_balls': [22, 23, 24, 25, 26, 27],
                    'blue_ball': 28,
                    'method': '冷球筛选'
                }
            },
            'comparison': {
                4: {
                    'max_hit': {
                        'period': '25008',
                        'total_hits': 6,
                        'red_hits': 5,
                        'blue_hits': 1,
                        'answer_red_balls': [22, 23, 24, 25, 26, 30],
                        'answer_blue_ball': 28,
                        'blue_hit_status': True
                    }
                }
            },
            'database_size': 100,
            'latest_period': {'period': '25003', 'red_balls': [3, 7, 12, 19, 22, 24], 'blue_ball': 11}
        }
    ]
    
    print("模拟数据包含:")
    print("- 25001期第1组: 6球命中 (红6+蓝0)")
    print("- 25001期第2组: 7球命中 (红6+蓝1)")
    print("- 25002期第3组: 5球命中 (红4+蓝1) - 不会显示")
    print("- 25003期第4组: 6球命中 (红5+蓝1)")
    
    # 调用高命中数详细信息打印功能
    print("\n调用 _print_high_hit_details 方法:")
    system._print_high_hit_details(mock_results)
    
    return True


def test_no_high_hits():
    """测试没有高命中数的情况"""
    print("\n=== 测试没有高命中数的情况 ===")
    
    # 创建系统实例
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 模拟没有6球或7球命中的结果
    mock_results = [
        {
            'period': '25010',
            'predictions': {
                1: {
                    'red_balls': [1, 2, 3, 4, 5, 6],
                    'blue_ball': 7,
                    'method': '历史出现概率'
                }
            },
            'comparison': {
                1: {
                    'max_hit': {
                        'period': '25015',
                        'total_hits': 4,
                        'red_hits': 3,
                        'blue_hits': 1,
                        'answer_red_balls': [1, 2, 3, 10, 11, 12],
                        'answer_blue_ball': 7,
                        'blue_hit_status': True
                    }
                }
            },
            'database_size': 100,
            'latest_period': {'period': '25010', 'red_balls': [4, 8, 13, 20, 23, 25], 'blue_ball': 9}
        }
    ]
    
    print("模拟数据包含:")
    print("- 25010期第1组: 4球命中 (红3+蓝1) - 不会显示")
    
    # 调用高命中数详细信息打印功能
    print("\n调用 _print_high_hit_details 方法:")
    system._print_high_hit_details(mock_results)
    
    return True


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 创建系统实例
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 测试空结果
    print("1. 测试空结果列表:")
    system._print_high_hit_details([])
    
    # 测试没有预测的结果
    print("\n2. 测试没有预测的结果:")
    empty_result = [{
        'period': '25020',
        'predictions': {},
        'comparison': {},
        'database_size': 100,
        'latest_period': {'period': '25020', 'red_balls': [5, 9, 14, 21, 24, 26], 'blue_ball': 12}
    }]
    system._print_high_hit_details(empty_result)
    
    return True


def main():
    """主函数"""
    print("测试高命中数详细信息打印功能")
    print("=" * 60)
    
    try:
        success1 = test_high_hit_details_function()
        success2 = test_no_high_hits()
        success3 = test_edge_cases()
        
        print("\n" + "=" * 60)
        if success1 and success2 and success3:
            print("🎉 高命中数详细信息打印功能测试成功！")
            print("\n功能特点:")
            print("1. ✅ 只显示6球和7球的高命中记录")
            print("2. ✅ 详细显示分析期号、预测组号、预测方法等信息")
            print("3. ✅ 显示预测号码和命中期号的实际号码")
            print("4. ✅ 显示红球和蓝球的具体命中数")
            print("5. ✅ 显示具体命中的红球号码")
            print("6. ✅ 处理没有高命中记录的情况")
            print("7. ✅ 格式化输出，易于阅读")
        else:
            print("❌ 高命中数详细信息打印功能测试失败")
        
        return success1 and success2 and success3
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
