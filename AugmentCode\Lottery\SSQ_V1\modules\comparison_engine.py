# -*- coding: utf-8 -*-
"""
比对引擎模块 (Comparison Engine Module)

负责将预测结果与实际开奖结果进行比对分析。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class ComparisonEngine:
    """
    比对引擎类
    
    负责预测结果与实际结果的比对分析
    """
    
    def __init__(self, config: Dict):
        """
        初始化比对引擎
        
        Args:
            config: 系统配置字典
        """
        self.config = config
    
    def compare_predictions(self, predictions: Dict, answer_data: List[Dict]) -> Dict:
        """
        比对预测结果与答案数据
        
        Args:
            predictions: 预测结果字典（24组预测）
            answer_data: 答案数据列表（6期开奖结果）
            
        Returns:
            比对结果字典
        """
        comparison_results = {}
        
        # 对每组预测进行比对
        for group_id, prediction in predictions.items():
            group_results = self._compare_single_prediction(prediction, answer_data)
            # 找到最大命中球数，如果有多个相同的最大值，选择期号最大的
            max_hit = max(group_results, key=lambda x: (x['total_hits'], int(x['period'])))

            comparison_results[group_id] = {
                'prediction': prediction,
                'results': group_results,
                'max_hit': max_hit
            }
        
        return comparison_results
    
    def _compare_single_prediction(self, prediction: Dict, answer_data: List[Dict]) -> List[Dict]:
        """
        比对单组预测与答案数据
        
        Args:
            prediction: 单组预测结果
            answer_data: 答案数据列表
            
        Returns:
            比对结果列表
        """
        results = []
        
        pred_red_balls = set(prediction['red_balls'])
        pred_blue_ball = prediction['blue_ball']
        
        for answer in answer_data:
            answer_red_balls = set(answer['red_balls'])
            answer_blue_ball = answer['blue_ball']
            
            # 计算红球命中数
            red_hits = len(pred_red_balls & answer_red_balls)
            
            # 计算蓝球命中数
            blue_hits = 1 if pred_blue_ball == answer_blue_ball else 0
            
            # 总命中数
            total_hits = red_hits + blue_hits
            
            results.append({
                'period': answer['period'],
                'red_hits': red_hits,
                'blue_hits': blue_hits,
                'total_hits': total_hits,
                'blue_hit_status': blue_hits == 1,
                'answer_red_balls': answer['red_balls'],
                'answer_blue_ball': answer['blue_ball']
            })
        
        return results
    
    def calculate_hit_statistics(self, comparison_results: Dict) -> Dict:
        """
        计算命中统计信息
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            统计信息字典
        """
        statistics = {}
        
        # 初始化统计计数器
        hit_distribution = {}
        for i in range(8):  # 0-7个球的命中情况
            hit_distribution[i] = 0
        
        blue_hit_count = 0
        total_predictions = 0
        
        # 统计每组预测的最大命中情况
        for group_id, group_result in comparison_results.items():
            max_hit_result = group_result['max_hit']
            max_hits = max_hit_result['total_hits']
            
            hit_distribution[max_hits] += 1
            
            if max_hit_result['blue_hit_status']:
                blue_hit_count += 1
            
            total_predictions += 1
        
        # 计算统计信息
        statistics = {
            'hit_distribution': hit_distribution,
            'blue_hit_count': blue_hit_count,
            'total_predictions': total_predictions,
            'blue_hit_rate': blue_hit_count / total_predictions if total_predictions > 0 else 0,
            'average_hits': sum(hits * count for hits, count in hit_distribution.items()) / total_predictions if total_predictions > 0 else 0
        }
        
        return statistics
    
    def get_detailed_analysis(self, comparison_results: Dict) -> Dict:
        """
        获取详细分析结果
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            详细分析结果字典
        """
        analysis = {
            'group_analysis': {},
            'method_analysis': {},
            'overall_statistics': self.calculate_hit_statistics(comparison_results)
        }
        
        # 按组分析
        for group_id, group_result in comparison_results.items():
            prediction = group_result['prediction']
            max_hit = group_result['max_hit']
            
            analysis['group_analysis'][group_id] = {
                'method': prediction['method'],
                'predicted_red_balls': prediction['red_balls'],
                'predicted_blue_ball': prediction['blue_ball'],
                'max_hits': max_hit['total_hits'],
                'max_hit_period': max_hit['period'],
                'max_hit_red_hits': max_hit['red_hits'],
                'max_hit_blue_hits': max_hit['blue_hits'],
                'blue_hit_status': max_hit['blue_hit_status'],
                'all_results': group_result['results']
            }
        
        # 按方法类型分析
        method_stats = {}
        for group_id, group_result in comparison_results.items():
            method = group_result['prediction']['method']
            max_hits = group_result['max_hit']['total_hits']
            
            if method not in method_stats:
                method_stats[method] = {
                    'total_hits': 0,
                    'count': 0,
                    'max_hits': 0,
                    'blue_hits': 0
                }
            
            method_stats[method]['total_hits'] += max_hits
            method_stats[method]['count'] += 1
            method_stats[method]['max_hits'] = max(method_stats[method]['max_hits'], max_hits)
            
            if group_result['max_hit']['blue_hit_status']:
                method_stats[method]['blue_hits'] += 1
        
        # 计算方法平均命中率
        for method, stats in method_stats.items():
            stats['average_hits'] = stats['total_hits'] / stats['count'] if stats['count'] > 0 else 0
            stats['blue_hit_rate'] = stats['blue_hits'] / stats['count'] if stats['count'] > 0 else 0
        
        analysis['method_analysis'] = method_stats
        
        return analysis
    
    def generate_comparison_summary(self, comparison_results: Dict) -> str:
        """
        生成比对结果摘要
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            摘要字符串
        """
        analysis = self.get_detailed_analysis(comparison_results)
        overall_stats = analysis['overall_statistics']
        
        summary_lines = [
            "=== 预测结果比对摘要 ===",
            f"总预测组数: {overall_stats['total_predictions']}",
            f"平均命中球数: {overall_stats['average_hits']:.2f}",
            f"蓝球命中次数: {overall_stats['blue_hit_count']}",
            f"蓝球命中率: {overall_stats['blue_hit_rate']:.2%}",
            "",
            "命中分布统计:",
        ]
        
        for hits, count in overall_stats['hit_distribution'].items():
            if count > 0:
                percentage = count / overall_stats['total_predictions'] * 100
                summary_lines.append(f"  {hits}球命中: {count}次 ({percentage:.1f}%)")
        
        summary_lines.extend([
            "",
            "最佳预测组:",
        ])
        
        # 找出最佳预测组
        best_groups = []
        max_hits = 0
        
        for group_id, group_result in comparison_results.items():
            hits = group_result['max_hit']['total_hits']
            if hits > max_hits:
                max_hits = hits
                best_groups = [group_id]
            elif hits == max_hits:
                best_groups.append(group_id)
        
        for group_id in best_groups:
            group_result = comparison_results[group_id]
            prediction = group_result['prediction']
            max_hit = group_result['max_hit']
            
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            summary_lines.append(
                f"  第{group_id}组 ({prediction['method']}): "
                f"{red_balls_str} + {prediction['blue_ball']} "
                f"最大命中{max_hits}球 (期号: {max_hit['period']})"
            )
        
        return '\n'.join(summary_lines)
    
    def export_comparison_data(self, comparison_results: Dict) -> List[Dict]:
        """
        导出比对数据用于Excel保存
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            导出数据列表
        """
        export_data = []
        
        for group_id, group_result in comparison_results.items():
            prediction = group_result['prediction']
            max_hit = group_result['max_hit']
            
            # 基本信息
            base_data = {
                'group_id': group_id,
                'method': prediction['method'],
                'predicted_red_balls': ' '.join(map(str, prediction['red_balls'])),
                'predicted_blue_ball': prediction['blue_ball'],
                'max_hits': max_hit['total_hits'],
                'max_hit_period': max_hit['period'],
                'max_hit_red_hits': max_hit['red_hits'],
                'max_hit_blue_hits': max_hit['blue_hits'],
                'blue_hit_status': '是' if max_hit['blue_hit_status'] else '否'
            }
            
            # 添加所有比对结果
            for i, result in enumerate(group_result['results'], 1):
                result_data = base_data.copy()
                result_data.update({
                    f'period_{i}': result['period'],
                    f'red_hits_{i}': result['red_hits'],
                    f'blue_hits_{i}': result['blue_hits'],
                    f'total_hits_{i}': result['total_hits'],
                    f'answer_red_balls_{i}': ' '.join(map(str, result['answer_red_balls'])),
                    f'answer_blue_ball_{i}': result['answer_blue_ball']
                })
            
            export_data.append(base_data)
        
        return export_data
